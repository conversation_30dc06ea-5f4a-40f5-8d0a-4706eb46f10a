:github_url: https://github.com/AI4Finance-Foundation/FinRL

3-Practical
========================


This section is recommended for users with some familiarity of FinRL or FinRL-Meta (or already run the notebooks in "1-Introduction"). User could use the provided code for specific task, or design their own task based on existing code.

Notebooks in this section includes:

`FinRL_MultiCrypto_Trading.ipynb <https://github.com/AI4Finance-Foundation/FinRL-Tutorials/blob/master/3-Practical/FinRL_MultiCrypto_Trading.ipynb>`_,

In this notebook, we provide a demo of multiple cryptocurrency trading. It shows a whole process of how to use APIs in FinRL and FinRL-Meta to do cryptocurrency trading.

`FinRL_PaperTrading_Demo.ipynb <https://github.com/AI4Finance-Foundation/FinRL-Tutorials/blob/master/3-Practical/FinRL_PaperTrading_Demo.ipynb>`_.

In this notebook, we provide a demo of paper trading. It shows a whole process of using FinRL and FinRL-Meta to connect to Alpaca to do paper trading. Note: User need to have their own Alpaca account to run this notebook.
