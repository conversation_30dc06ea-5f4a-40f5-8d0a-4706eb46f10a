:github_url: https://github.com/AI4Finance-Foundation/FinRL

1-Introduction
========================

This section is recommend for new comers of FinRL. Users could better learn FinRL in the meantime of running these notebooks.

1. `Stock_NeurIPS2018.ipynb <https://github.com/AI4Finance-Foundation/FinRL-Tutorials/blob/master/1-Introduction/Stock_NeurIPS2018_SB3.ipynb>`_,

This is the notebook we recommend new users run first. It goes through a full process of DRL for stock trading using FinRL.


2. `China_A_share_market_tushare.ipynb <https://github.com/AI4Finance-Foundation/FinRL-Tutorials/blob/master/1-Introduction/China_A_share_market_tushare.ipynb>`_

This notebook demonstrate using FinRL to connect Tushare, using its data of China A share market.

3. `FinRL_PortfolioAllocation_NeurIPS_2020.ipynb <https://github.com/AI4Finance-Foundation/FinRL-Tutorials/blob/master/1-Introduction/FinRL_PortfolioAllocation_NeurIPS_2020.ipynb>`_

This notebook demonstrate using FinRL to do portfolio allocation.
