{"cells": [{"cell_type": "markdown", "metadata": {"id": "QMjwq6pS-kFz"}, "source": ["# Stock NeurIPS2018 Part 2. Train\n", "This series is a reproduction of *the process in the paper Practical Deep Reinforcement Learning Approach for Stock Trading*. \n", "\n", "This is the second part of the NeurIPS2018 series, introducing how to use FinRL to make data into the gym form environment, and train DRL agents on it.\n", "\n", "Other demos can be found at the repo of [FinRL-Tutorials]((https://github.com/AI4Finance-Foundation/FinRL-Tutorials))."]}, {"cell_type": "markdown", "metadata": {"id": "gT-zXutMgqOS"}, "source": ["# Part 1. Install Packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "D0vEcPxSJ8hI"}, "outputs": [], "source": ["## install finrl library\n", "!pip install git+https://github.com/AI4Finance-Foundation/FinRL.git"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "xt1317y2ixSS"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/opt/anaconda3/envs/finance/lib/python3.8/site-packages/tqdm/auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import pandas as pd\n", "from stable_baselines3.common.logger import configure\n", "\n", "from finrl.agents.stablebaselines3.models import DRLAgent\n", "from finrl.config import INDICATORS, TRAINED_MODEL_DIR, RESULTS_DIR\n", "from finrl.main import check_and_make_directories\n", "from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv\n", "\n", "check_and_make_directories([TRAINED_MODEL_DIR])"]}, {"cell_type": "markdown", "metadata": {"id": "aWrSrQv3i0Ng"}, "source": ["# Part 2. Build A Market Environment in OpenAI Gym-style"]}, {"cell_type": "markdown", "metadata": {"id": "wiHhM2U-XBMZ"}, "source": ["![rl_diagram_transparent_bg.png](data:image/png;base64,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)"]}, {"cell_type": "markdown", "metadata": {"id": "Lene<PERSON>vy"}, "source": ["The core element in reinforcement learning are **agent** and **environment**. You can understand RL as the following process: \n", "\n", "The agent is active in a world, which is the environment. It observe its current condition as a **state**, and is allowed to do certain **actions**. After the agent execute an action, it will arrive at a new state. At the same time, the environment will have feedback to the agent called **reward**, a numerical signal that tells how good or bad the new state is. As the figure above, agent and environment will keep doing this interaction.\n", "\n", "The goal of agent is to get as much cumulative reward as possible. Reinforcement learning is the method that agent learns to improve its behavior and achieve that goal."]}, {"cell_type": "markdown", "metadata": {"id": "w3H88JXkI93v"}, "source": ["To achieve this in Python, we follow the OpenAI gym style to build the stock data into environment.\n", "\n", "state-action-reward are specified as follows:\n", "\n", "* **State s**: The state space represents an agent's perception of the market environment. Just like a human trader analyzing various information, here our agent passively observes the price data and technical indicators based on the past data. It will learn by interacting with the market environment (usually by replaying historical data).\n", "\n", "* **Action a**: The action space includes allowed actions that an agent can take at each state. For example, a ∈ {−1, 0, 1}, where −1, 0, 1 represent\n", "selling, holding, and buying. When an action operates multiple shares, a ∈{−k, ..., −1, 0, 1, ..., k}, e.g.. \"Buy 10 shares of AAPL\" or \"Sell 10 shares of AAPL\" are 10 or −10, respectively\n", "\n", "* **Reward function r(s, a, s′)**: Reward is an incentive for an agent to learn a better policy. For example, it can be the change of the portfolio value when taking a at state s and arriving at new state s',  i.e., r(s, a, s′) = v′ − v, where v′ and v represent the portfolio values at state s′ and s, respectively\n", "\n", "\n", "**Market environment**: 30 constituent stocks of Dow Jones Industrial Average (DJIA) index. Accessed at the starting date of the testing period."]}, {"cell_type": "markdown", "metadata": {"id": "SKyZejI0fmp1"}, "source": ["## Read data\n", "\n", "We first read the .csv file of our training data into dataframe."]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "mFCP1YEhi6oi"}, "outputs": [], "source": ["train = pd.read_csv('train_data.csv')\n", "# If you are not using the data generated from part 1 of this tutorial, make sure \n", "# it has the columns and index in the form that could be make into the environment. \n", "# Then you can comment and skip the following two lines.\n", "train = train.set_index(train.columns[0])\n", "train.index.names = ['']"]}, {"cell_type": "markdown", "metadata": {"id": "Yw95ZMicgEyi"}, "source": ["## Construct the environment"]}, {"cell_type": "markdown", "metadata": {"id": "5WZ6-9q2gq9S"}, "source": ["Calculate and specify the parameters we need for constructing the environment."]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7T3DZPoaIm8k", "outputId": "4817e063-400a-416e-f8f2-4b1c4d9c8408"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stock Dimension: 29, State Space: 291\n"]}], "source": ["stock_dimension = len(train.tic.unique())\n", "state_space = 1 + 2*stock_dimension + len(INDICATORS)*stock_dimension\n", "print(f\"Stock Dimension: {stock_dimension}, State Space: {state_space}\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "WsOLoeNcJF8Q"}, "outputs": [], "source": ["buy_cost_list = sell_cost_list = [0.001] * stock_dimension\n", "num_stock_shares = [0] * stock_dimension\n", "\n", "env_kwargs = {\n", "    \"hmax\": 100,\n", "    \"initial_amount\": 1000000,\n", "    \"num_stock_shares\": num_stock_shares,\n", "    \"buy_cost_pct\": buy_cost_list,\n", "    \"sell_cost_pct\": sell_cost_list,\n", "    \"state_space\": state_space,\n", "    \"stock_dim\": stock_dimension,\n", "    \"tech_indicator_list\": INDICATORS,\n", "    \"action_space\": stock_dimension,\n", "    \"reward_scaling\": 1e-4\n", "}\n", "\n", "\n", "e_train_gym = StockTradingEnv(df = train, **env_kwargs)"]}, {"cell_type": "markdown", "metadata": {"id": "7We-q73jjaFQ"}, "source": ["## Environment for training"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aS-SHiGRJK-4", "outputId": "a733ecdf-d857-40f5-b399-4325c7ead299"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'stable_baselines3.common.vec_env.dummy_vec_env.DummyVecEnv'>\n"]}], "source": ["env_train, _ = e_train_gym.get_sb_env()\n", "print(type(env_train))"]}, {"cell_type": "markdown", "metadata": {"id": "HMNR5nHjh1iz"}, "source": ["# Part 3: Train DRL Agents\n", "* Here, the DRL algorithms are from **[Stable Baselines 3](https://stable-baselines3.readthedocs.io/en/master/)**. It's a library that implemented popular DRL algorithms using pytorch, succeeding to its old version: Stable Baselines.\n", "* Users are also encouraged to try **[ElegantRL](https://github.com/AI4Finance-Foundation/ElegantRL)** and **[<PERSON> RLlib](https://github.com/ray-project/ray)**."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "364PsqckttcQ"}, "outputs": [], "source": ["agent = DRLAgent(env = env_train)\n", "\n", "# Set the corresponding values to 'True' for the algorithms that you want to use\n", "if_using_a2c = True\n", "if_using_ddpg = True\n", "if_using_ppo = True\n", "if_using_td3 = True\n", "if_using_sac = True"]}, {"cell_type": "markdown", "metadata": {"id": "YDmqOyF9h1iz"}, "source": ["## Agent Training: 5 algorithms (A2C, DDPG, PPO, TD3, SAC)\n"]}, {"cell_type": "markdown", "metadata": {"id": "uijiWgkuh1jB"}, "source": ["### Agent 1: A2C\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "GUCnkn-HIbmj", "outputId": "2794a094-a916-448c-ead1-6e20184dde2a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'n_steps': 5, 'ent_coef': 0.01, 'learning_rate': 0.0007}\n", "Using cpu device\n", "Logging to results/a2c\n"]}], "source": ["agent = DRLAgent(env = env_train)\n", "model_a2c = agent.get_model(\"a2c\")\n", "\n", "if if_using_a2c:\n", "  # set up logger\n", "  tmp_path = RESULTS_DIR + '/a2c'\n", "  new_logger_a2c = configure(tmp_path, [\"stdout\", \"csv\", \"tensorboard\"])\n", "  # Set new logger\n", "  model_a2c.set_logger(new_logger_a2c)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "0GVpkWGqH4-D", "outputId": "f29cf145-e3b5-4e59-f64d-5921462a8f81"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 100        |\n", "|    time_elapsed       | 8          |\n", "|    total_timesteps    | 500        |\n", "| train/                |            |\n", "|    entropy_loss       | -41.7      |\n", "|    explained_variance | -1.19e-07  |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 3454       |\n", "|    policy_loss        | -5.75      |\n", "|    reward             | 0.10798945 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 0.113      |\n", "--------------------------------------\n", "----------------------------------------\n", "| time/                 |              |\n", "|    fps                | 65           |\n", "|    iterations         | 200          |\n", "|    time_elapsed       | 15           |\n", "|    total_timesteps    | 1000         |\n", "| train/                |              |\n", "|    entropy_loss       | -41.7        |\n", "|    explained_variance | 0            |\n", "|    learning_rate      | 0.0007       |\n", "|    n_updates          | 3554         |\n", "|    policy_loss        | -95.5        |\n", "|    reward             | -0.075115256 |\n", "|    std                | 1.02         |\n", "|    value_loss         | 6.41         |\n", "----------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 71       |\n", "|    iterations         | 300      |\n", "|    time_elapsed       | 20       |\n", "|    total_timesteps    | 1500     |\n", "| train/                |          |\n", "|    entropy_loss       | -41.7    |\n", "|    explained_variance | 0        |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 3654     |\n", "|    policy_loss        | -266     |\n", "|    reward             | 4.334886 |\n", "|    std                | 1.02     |\n", "|    value_loss         | 44.6     |\n", "------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 68        |\n", "|    iterations         | 400       |\n", "|    time_elapsed       | 29        |\n", "|    total_timesteps    | 2000      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.7     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 3754      |\n", "|    policy_loss        | -42.1     |\n", "|    reward             | 0.9876081 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 1.81      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 71         |\n", "|    iterations         | 500        |\n", "|    time_elapsed       | 34         |\n", "|    total_timesteps    | 2500       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.6      |\n", "|    explained_variance | 2.38e-07   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 3854       |\n", "|    policy_loss        | 474        |\n", "|    reward             | -13.375484 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 169        |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 70        |\n", "|    iterations         | 600       |\n", "|    time_elapsed       | 42        |\n", "|    total_timesteps    | 3000      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.6     |\n", "|    explained_variance | -0.733    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 3954      |\n", "|    policy_loss        | 136       |\n", "|    reward             | 0.2225512 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 15.3      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 71        |\n", "|    iterations         | 700       |\n", "|    time_elapsed       | 48        |\n", "|    total_timesteps    | 3500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.6     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4054      |\n", "|    policy_loss        | -118      |\n", "|    reward             | -5.508063 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 10.1      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 73         |\n", "|    iterations         | 800        |\n", "|    time_elapsed       | 54         |\n", "|    total_timesteps    | 4000       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.6      |\n", "|    explained_variance | -0.0931    |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 4154       |\n", "|    policy_loss        | 10.4       |\n", "|    reward             | -1.2465808 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 0.901      |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 72          |\n", "|    iterations         | 900         |\n", "|    time_elapsed       | 62          |\n", "|    total_timesteps    | 4500        |\n", "| train/                |             |\n", "|    entropy_loss       | -41.7       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 4254        |\n", "|    policy_loss        | 111         |\n", "|    reward             | -0.92076373 |\n", "|    std                | 1.02        |\n", "|    value_loss         | 8.6         |\n", "---------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 73         |\n", "|    iterations         | 1000       |\n", "|    time_elapsed       | 68         |\n", "|    total_timesteps    | 5000       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.7      |\n", "|    explained_variance | -1.19e-07  |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 4354       |\n", "|    policy_loss        | 72.2       |\n", "|    reward             | -6.5418406 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 4.29       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 71        |\n", "|    iterations         | 1100      |\n", "|    time_elapsed       | 76        |\n", "|    total_timesteps    | 5500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.8     |\n", "|    explained_variance | 0.00334   |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4454      |\n", "|    policy_loss        | -498      |\n", "|    reward             | 6.0074706 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 150       |\n", "-------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 71          |\n", "|    iterations         | 1200        |\n", "|    time_elapsed       | 84          |\n", "|    total_timesteps    | 6000        |\n", "| train/                |             |\n", "|    entropy_loss       | -41.7       |\n", "|    explained_variance | -3.3e-05    |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 4554        |\n", "|    policy_loss        | -168        |\n", "|    reward             | -0.22433381 |\n", "|    std                | 1.02        |\n", "|    value_loss         | 19.6        |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 71        |\n", "|    iterations         | 1300      |\n", "|    time_elapsed       | 90        |\n", "|    total_timesteps    | 6500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.8     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4654      |\n", "|    policy_loss        | 199       |\n", "|    reward             | -5.576095 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 47.8      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 71        |\n", "|    iterations         | 1400      |\n", "|    time_elapsed       | 98        |\n", "|    total_timesteps    | 7000      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.8     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4754      |\n", "|    policy_loss        | 224       |\n", "|    reward             | -1.378166 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 32.2      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 72        |\n", "|    iterations         | 1500      |\n", "|    time_elapsed       | 104       |\n", "|    total_timesteps    | 7500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.8     |\n", "|    explained_variance | -1.19e-07 |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4854      |\n", "|    policy_loss        | -139      |\n", "|    reward             | 4.8697376 |\n", "|    std                | 1.03      |\n", "|    value_loss         | 11.3      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 71        |\n", "|    iterations         | 1600      |\n", "|    time_elapsed       | 112       |\n", "|    total_timesteps    | 8000      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.8     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4954      |\n", "|    policy_loss        | 214       |\n", "|    reward             | 1.2550246 |\n", "|    std                | 1.03      |\n", "|    value_loss         | 29.8      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 71        |\n", "|    iterations         | 1700      |\n", "|    time_elapsed       | 118       |\n", "|    total_timesteps    | 8500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.8     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 5054      |\n", "|    policy_loss        | -173      |\n", "|    reward             | 3.4403105 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 42.3      |\n", "-------------------------------------\n", "day: 2892, episode: 10\n", "begin_total_asset: 1000000.00\n", "end_total_asset: 6470641.90\n", "total_reward: 5470641.90\n", "total_cost: 41812.90\n", "total_trades: 48852\n", "Sharpe: 0.814\n", "=================================\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 71        |\n", "|    iterations         | 1800      |\n", "|    time_elapsed       | 126       |\n", "|    total_timesteps    | 9000      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.8     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 5154      |\n", "|    policy_loss        | -45.2     |\n", "|    reward             | 1.8548855 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 1.32      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 71         |\n", "|    iterations         | 1900       |\n", "|    time_elapsed       | 132        |\n", "|    total_timesteps    | 9500       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.8      |\n", "|    explained_variance | -1.19e-07  |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 5254       |\n", "|    policy_loss        | -103       |\n", "|    reward             | -0.2859979 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 8.36       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 72         |\n", "|    iterations         | 2000       |\n", "|    time_elapsed       | 138        |\n", "|    total_timesteps    | 10000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.8      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 5354       |\n", "|    policy_loss        | -226       |\n", "|    reward             | 0.73303264 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 41.7       |\n", "--------------------------------------\n"]}], "source": ["trained_a2c = agent.train_model(model=model_a2c, \n", "                             tb_log_name='a2c',\n", "                             total_timesteps=50000) if if_using_a2c else None"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "zjCWfgsg3sVa"}, "outputs": [], "source": ["trained_a2c.save(TRAINED_MODEL_DIR + \"/agent_a2c\") if if_using_a2c else None"]}, {"cell_type": "markdown", "metadata": {"id": "MRiOtrywfAo1"}, "source": ["### Agent 2: DDPG"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "M2YadjfnLwgt"}, "outputs": [], "source": ["agent = DRLAgent(env = env_train)\n", "model_ddpg = agent.get_model(\"ddpg\")\n", "\n", "if if_using_ddpg:\n", "  # set up logger\n", "  tmp_path = RESULTS_DIR + '/ddpg'\n", "  new_logger_ddpg = configure(tmp_path, [\"stdout\", \"csv\", \"tensorboard\"])\n", "  # Set new logger\n", "  model_ddpg.set_logger(new_logger_ddpg)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tCDa78rqfO_a"}, "outputs": [], "source": ["trained_ddpg = agent.train_model(model=model_ddpg, \n", "                             tb_log_name='ddpg',\n", "                             total_timesteps=50000) if if_using_ddpg else None"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ne6M2R-WvrUQ"}, "outputs": [], "source": ["trained_ddpg.save(TRAINED_MODEL_DIR + \"/agent_ddpg\") if if_using_ddpg else None"]}, {"cell_type": "markdown", "metadata": {"id": "_gDkU-j-fCmZ"}, "source": ["### Agent 3: PPO"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "y5D5PFUhMzSV"}, "outputs": [], "source": ["agent = DRLAgent(env = env_train)\n", "PPO_PARAMS = {\n", "    \"n_steps\": 2048,\n", "    \"ent_coef\": 0.01,\n", "    \"learning_rate\": 0.00025,\n", "    \"batch_size\": 128,\n", "}\n", "model_ppo = agent.get_model(\"ppo\",model_kwargs = PPO_PARAMS)\n", "\n", "if if_using_ppo:\n", "  # set up logger\n", "  tmp_path = RESULTS_DIR + '/ppo'\n", "  new_logger_ppo = configure(tmp_path, [\"stdout\", \"csv\", \"tensorboard\"])\n", "  # Set new logger\n", "  model_ppo.set_logger(new_logger_ppo)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Gt8eIQKYM4G3"}, "outputs": [], "source": ["trained_ppo = agent.train_model(model=model_ppo, \n", "                             tb_log_name='ppo',\n", "                             total_timesteps=200000) if if_using_ppo else None"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "C6AidlWyvwzm"}, "outputs": [], "source": ["trained_ppo.save(TRAINED_MODEL_DIR + \"/agent_ppo\") if if_using_ppo else None"]}, {"cell_type": "markdown", "metadata": {"id": "3Zpv4S0-fDBv"}, "source": ["### Agent 4: TD3"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JSAHhV4Xc-bh"}, "outputs": [], "source": ["agent = DRLAgent(env = env_train)\n", "TD3_PARAMS = {\"batch_size\": 100, \n", "              \"buffer_size\": 1000000, \n", "              \"learning_rate\": 0.001}\n", "\n", "model_td3 = agent.get_model(\"td3\",model_kwargs = TD3_PARAMS)\n", "\n", "if if_using_td3:\n", "  # set up logger\n", "  tmp_path = RESULTS_DIR + '/td3'\n", "  new_logger_td3 = configure(tmp_path, [\"stdout\", \"csv\", \"tensorboard\"])\n", "  # Set new logger\n", "  model_td3.set_logger(new_logger_td3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "OSRxNYAxdKpU"}, "outputs": [], "source": ["trained_td3 = agent.train_model(model=model_td3, \n", "                             tb_log_name='td3',\n", "                             total_timesteps=50000) if if_using_td3 else None"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "OkJV6V_mv2hw"}, "outputs": [], "source": ["trained_td3.save(TRAINED_MODEL_DIR + \"/agent_td3\") if if_using_td3 else None"]}, {"cell_type": "markdown", "metadata": {"id": "Dr49PotrfG01"}, "source": ["### Agent 5: SAC"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "xwOhVjqRkCdM"}, "outputs": [], "source": ["agent = DRLAgent(env = env_train)\n", "SAC_PARAMS = {\n", "    \"batch_size\": 128,\n", "    \"buffer_size\": 100000,\n", "    \"learning_rate\": 0.0001,\n", "    \"learning_starts\": 100,\n", "    \"ent_coef\": \"auto_0.1\",\n", "}\n", "\n", "model_sac = agent.get_model(\"sac\",model_kwargs = SAC_PARAMS)\n", "\n", "if if_using_sac:\n", "  # set up logger\n", "  tmp_path = RESULTS_DIR + '/sac'\n", "  new_logger_sac = configure(tmp_path, [\"stdout\", \"csv\", \"tensorboard\"])\n", "  # Set new logger\n", "  model_sac.set_logger(new_logger_sac)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "K8RSdKCckJyH"}, "outputs": [], "source": ["trained_sac = agent.train_model(model=model_sac, \n", "                             tb_log_name='sac',\n", "                             total_timesteps=70000) if if_using_sac else None"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_SpZoQgPv7GO"}, "outputs": [], "source": ["trained_sac.save(TRAINED_MODEL_DIR + \"/agent_sac\") if if_using_sac else None"]}, {"cell_type": "markdown", "metadata": {"id": "PgGm3dQZfRks"}, "source": ["## Save the trained agent\n", "Trained agents should have already been saved in the \"trained_models\" drectory after you run the code blocks above.\n", "\n", "For Colab users, the zip files should be at \"./trained_models\" or \"/content/trained_models\".\n", "\n", "For users running on your local environment, the zip files should be at \"./trained_models\"."]}], "metadata": {"colab": {"collapsed_sections": ["MRiOtrywfAo1", "_gDkU-j-fCmZ", "3Zpv4S0-fDBv", "Dr49PotrfG01"], "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 1}