{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "collapsed_sections": ["GfZ5vY5wRjkJ"]}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# Stock NeurIPS2018 Part 3. Backtest\n", "This series is a reproduction of paper *the process in the paper Practical Deep Reinforcement Learning Approach for Stock Trading*. \n", "\n", "This is the third and last part of the NeurIPS2018 series, introducing how to use use the agents we trained to do backtest, and compare with baselines such as Mean Variance Optimization and DJIA index.\n", "\n", "Other demos can be found at the repo of [FinRL-Tutorials]((https://github.com/AI4Finance-Foundation/FinRL-Tutorials))."], "metadata": {"id": "v7Cycmf3Zbok"}}, {"cell_type": "markdown", "source": ["# Part 1. Install Packages"], "metadata": {"id": "1oWbj4HgqHBg"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QJgoEYx3p_NG"}, "outputs": [], "source": ["## install required packages\n", "!pip install swig\n", "!pip install wrds\n", "!pip install pyportfolioopt\n", "## install finrl library\n", "!pip install git+https://github.com/AI4Finance-Foundation/FinRL.git"]}, {"cell_type": "code", "source": ["import sys\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "from finrl.meta.preprocessor.yahoodownloader import YahooDownloader\n", "from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv\n", "from finrl.agents.stablebaselines3.models import DRLAgent\n", "from stable_baselines3 import A2C, DDPG, PPO, SAC, TD3\n", "\n", "%matplotlib inline\n", "from finrl.config import INDICATORS"], "metadata": {"id": "mqfBOKz-qJYF"}, "execution_count": 38, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "kyv8fz5rqM7H"}, "execution_count": 3, "outputs": []}, {"cell_type": "markdown", "source": ["# Part 2. Backtesting"], "metadata": {"id": "mUF2P4hmqVjh"}}, {"cell_type": "markdown", "source": ["To backtest the agents, upload trade_data.csv in the same directory of this notebook. For Colab users, just upload trade_data.csv to the default directory."], "metadata": {"id": "BdU6qLsVWDxI"}}, {"cell_type": "code", "source": ["train = pd.read_csv('train_data.csv')\n", "trade = pd.read_csv('trade_data.csv')\n", "\n", "# If you are not using the data generated from part 1 of this tutorial, make sure \n", "# it has the columns and index in the form that could be make into the environment. \n", "# Then you can comment and skip the following lines.\n", "train = train.set_index(train.columns[0])\n", "train.index.names = ['']\n", "trade = trade.set_index(trade.columns[0])\n", "trade.index.names = ['']"], "metadata": {"id": "mSjBHn_MZr4U"}, "execution_count": 10, "outputs": []}, {"cell_type": "markdown", "source": ["Then, upload the trained agent to the same directory, and set the corresponding variable to True."], "metadata": {"id": "qu4Ey54b36oL"}}, {"cell_type": "code", "source": ["if_using_a2c = True\n", "if_using_ddpg = False\n", "if_using_ppo = False\n", "if_using_td3 = False\n", "if_using_sac = False"], "metadata": {"id": "Z_mVZM4IIa55"}, "execution_count": 4, "outputs": []}, {"cell_type": "markdown", "source": ["Load the agents"], "metadata": {"id": "73D4oRqAIkYj"}}, {"cell_type": "code", "source": ["trained_a2c = A2C.load(\"trained_models/agent_a2c\") if if_using_a2c else None\n", "trained_ddpg = DDPG.load(\"trained_models/agent_ddpg\") if if_using_ddpg else None\n", "trained_ppo = PPO.load(\"trained_models/agent_ppo\") if if_using_ppo else None\n", "trained_td3 = TD3.load(\"trained_models/agent_td3\") if if_using_td3 else None\n", "trained_sac = SAC.load(\"trained_models/agent_sac\") if if_using_sac else None"], "metadata": {"id": "6CagrX0I36ZN"}, "execution_count": 43, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "U5mmgQF_h1jQ"}, "source": ["### Trading (Out-of-sample Performance)\n", "\n", "We update periodically in order to take full advantage of the data, e.g., retrain quarterly, monthly or weekly. We also tune the parameters along the way, in this notebook we use the in-sample data from 2009-01 to 2020-07 to tune the parameters once, so there is some alpha decay here as the length of trade date extends. \n", "\n", "Numerous hyperparameters – e.g. the learning rate, the total number of samples to train on – influence the learning process and are usually determined by testing some variations."]}, {"cell_type": "code", "source": ["stock_dimension = len(trade.tic.unique())\n", "state_space = 1 + 2*stock_dimension + len(INDICATORS)*stock_dimension\n", "print(f\"Stock Dimension: {stock_dimension}, State Space: {state_space}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4H_w3SaBAkKU", "outputId": "fdaed3a7-d3a9-4cde-d194-ee4576057175"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Stock Dimension: 29, State Space: 291\n"]}]}, {"cell_type": "code", "source": ["buy_cost_list = sell_cost_list = [0.001] * stock_dimension\n", "num_stock_shares = [0] * stock_dimension\n", "\n", "env_kwargs = {\n", "    \"hmax\": 100,\n", "    \"initial_amount\": 1000000,\n", "    \"num_stock_shares\": num_stock_shares,\n", "    \"buy_cost_pct\": buy_cost_list,\n", "    \"sell_cost_pct\": sell_cost_list,\n", "    \"state_space\": state_space,\n", "    \"stock_dim\": stock_dimension,\n", "    \"tech_indicator_list\": INDICATORS,\n", "    \"action_space\": stock_dimension,\n", "    \"reward_scaling\": 1e-4\n", "}"], "metadata": {"id": "nKNmQMqGAknW"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "cIqoV0GSI52v"}, "outputs": [], "source": ["e_trade_gym = StockTradingEnv(df = trade, turbulence_threshold = 70,risk_indicator_col='vix', **env_kwargs)\n", "# env_trade, obs_trade = e_trade_gym.get_sb_env()"]}, {"cell_type": "code", "source": ["df_account_value_a2c, df_actions_a2c = DRLAgent.DRL_prediction(\n", "    model=trained_a2c, \n", "    environment = e_trade_gym) if if_using_a2c else (None, None)"], "metadata": {"id": "lbFchno5j3xs", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "44fffa47-3b47-4e7b-96c2-0a485e9efead"}, "execution_count": 44, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["hit end!\n"]}]}, {"cell_type": "code", "source": ["df_account_value_ddpg, df_actions_ddpg = DRLAgent.DRL_prediction(\n", "    model=trained_ddpg, \n", "    environment = e_trade_gym) if if_using_ddpg else (None, None)"], "metadata": {"id": "JbYljWGjj3pH"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["df_account_value_ppo, df_actions_ppo = DRLAgent.DRL_prediction(\n", "    model=trained_ppo, \n", "    environment = e_trade_gym) if if_using_ppo else (None, None)"], "metadata": {"id": "74jNP2DBj3hb"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["df_account_value_td3, df_actions_td3 = DRLAgent.DRL_prediction(\n", "    model=trained_td3, \n", "    environment = e_trade_gym) if if_using_td3 else (None, None)"], "metadata": {"id": "S7VyGGJPj3SH"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eLOnL5eYh1jR", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "70e50e24-aed5-49f9-cdd7-de6b9689d9ce"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["hit end!\n"]}], "source": ["df_account_value_sac, df_actions_sac = DRLAgent.DRL_prediction(\n", "    model=trained_sac, \n", "    environment = e_trade_gym) if if_using_sac else (None, None)"]}, {"cell_type": "markdown", "metadata": {"id": "GcE-t08w6DaW"}, "source": ["# Part 3: Mean Variance Optimization"]}, {"cell_type": "markdown", "source": ["Mean Variance optimization is a very classic strategy in portfolio management. Here, we go through the whole process to do the mean variance optimization and add it as a baseline to compare.\n", "\n", "First, process dataframe to the form for MVO weight calculation."], "metadata": {"id": "17TUs71EWj09"}}, {"cell_type": "code", "source": ["def process_df_for_mvo(df):\n", "  df = df.sort_values(['date','tic'],ignore_index=True)[['date','tic','close']]\n", "  fst = df\n", "  fst = fst.iloc[0:stock_dimension, :]\n", "  tic = fst['tic'].tolist()\n", "\n", "  mvo = pd.DataFrame()\n", "\n", "  for k in range(len(tic)):\n", "    mvo[tic[k]] = 0\n", "\n", "  for i in range(df.shape[0]//stock_dimension):\n", "    n = df\n", "    n = n.iloc[i * stock_dimension:(i+1) * stock_dimension, :]\n", "    date = n['date'][i*stock_dimension]\n", "    mvo.loc[date] = n['close'].tolist()\n", "  \n", "  return mvo"], "metadata": {"id": "wungSNOwPwKR"}, "execution_count": 16, "outputs": []}, {"cell_type": "markdown", "source": ["### Helper functions for mean returns and variance-covariance matrix"], "metadata": {"id": "SwEwkHJ1d_6u"}}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "6KvXkpyE8MFq"}, "outputs": [], "source": ["# Codes in this section partially refer to Dr G <PERSON><PERSON>\n", "\n", "# https://www.kaggle.com/code/vijipai/lesson-5-mean-variance-optimization-of-portfolios/notebook\n", "\n", "def StockReturnsComputing(StockPrice, Rows, Columns): \n", "  import numpy as np \n", "  StockReturn = np.zeros([Rows-1, Columns]) \n", "  for j in range(Columns):        # j: Assets \n", "    for i in range(Rows-1):     # i: Daily Prices \n", "      StockReturn[i,j]=((StockPrice[i+1, j]-StockPrice[i,j])/StockPrice[i,j])* 100 \n", "      \n", "  return StockReturn"]}, {"cell_type": "markdown", "source": ["### Calculate the weights for mean-variance"], "metadata": {"id": "IeVVbuwveJ_5"}}, {"cell_type": "code", "source": ["StockData = process_df_for_mvo(train)\n", "TradeData = process_df_for_mvo(trade)\n", "\n", "TradeData.to_numpy()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kE8nruKLQYLO", "outputId": "42d07c80-f309-49f8-f2b4-36a51987086f"}, "execution_count": 17, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([[ 89.49456024, 234.61433411,  91.07846832, ...,  47.76709366,\n", "         36.29002762, 115.09746552],\n", "       [ 89.49456024, 237.48353577,  91.34962463, ...,  47.87194443,\n", "         37.26651764, 114.63587189],\n", "       [ 91.88857269, 235.65348816,  93.52853394, ...,  48.26512146,\n", "         38.31402969, 114.32817841],\n", "       ...,\n", "       [147.34197998, 197.85211182, 179.35401917, ...,  49.07957077,\n", "         45.98464966, 147.1452179 ],\n", "       [148.01602173, 198.85264587, 178.01608276, ...,  49.54628754,\n", "         45.13446426, 145.86064148],\n", "       [147.55014038, 196.8515625 , 175.14343262, ...,  49.12624741,\n", "         44.02268982, 144.66435242]])"]}, "metadata": {}, "execution_count": 17}]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "u6_O6vrn_uD4", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "0c2f8bf7-07e7-4fe5-c409-93312b95a8dd"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mean returns of assets in k-portfolio 1\n", " [0.136 0.068 0.086 0.083 0.066 0.134 0.06  0.035 0.072 0.056 0.103 0.073\n", " 0.033 0.076 0.047 0.073 0.042 0.056 0.054 0.056 0.103 0.089 0.041 0.053\n", " 0.104 0.11  0.044 0.042 0.042]\n", "Variance-Covariance matrix of returns\n", " [[3.156 1.066 1.768 1.669 1.722 1.814 1.569 1.302 1.302 1.811 1.303 1.432\n", "  1.218 1.674 0.74  1.839 0.719 0.884 1.241 0.823 1.561 1.324 0.752 1.027\n", "  1.298 1.466 0.657 1.078 0.631]\n", " [1.066 2.571 1.306 1.123 1.193 1.319 1.116 1.053 1.045 1.269 1.068 1.089\n", "  0.899 1.218 0.926 1.391 0.682 0.727 1.025 1.156 1.166 0.984 0.798 0.956\n", "  1.259 1.111 0.688 1.091 0.682]\n", " [1.768 1.306 4.847 2.73  2.6   2.128 1.944 2.141 2.17  3.142 1.932 2.283\n", "  1.56  2.012 0.993 3.707 1.094 1.319 1.845 1.236 1.899 1.894 1.041 1.921\n", "  1.823 2.314 0.986 1.421 0.707]\n", " [1.669 1.123 2.73  4.892 2.363 1.979 1.7   2.115 1.959 2.387 1.773 2.319\n", "  1.571 1.797 0.968 2.597 1.144 1.298 1.643 1.071 1.615 1.775 0.91  1.666\n", "  1.707 1.784 0.82  1.345 0.647]\n", " [1.722 1.193 2.6   2.363 4.019 2.127 1.917 2.059 1.817 2.46  1.577 2.238\n", "  1.513 1.929 0.925 2.64  0.947 0.971 1.894 1.089 1.711 1.642 0.865 1.456\n", "  1.478 1.687 0.92  1.326 0.697]\n", " [1.814 1.319 2.128 1.979 2.127 5.384 1.974 1.549 1.683 2.122 1.624 1.771\n", "  1.441 1.939 0.846 2.191 0.837 1.075 1.475 1.041 1.978 1.768 0.784 1.328\n", "  1.365 1.912 0.787 1.28  0.666]\n", " [1.569 1.116 1.944 1.7   1.917 1.974 3.081 1.483 1.534 1.937 1.367 1.62\n", "  1.399 1.843 0.894 2.057 0.794 0.905 1.438 1.014 1.72  1.382 0.865 1.206\n", "  1.273 1.488 0.811 1.173 0.753]\n", " [1.302 1.053 2.141 2.115 2.059 1.549 1.483 2.842 1.525 2.044 1.428 1.783\n", "  1.308 1.533 0.878 2.279 0.938 1.092 1.385 1.078 1.429 1.314 0.831 1.459\n", "  1.466 1.48  0.83  1.042 0.567]\n", " [1.302 1.045 2.17  1.959 1.817 1.683 1.534 1.525 2.661 1.987 1.454 1.748\n", "  1.217 1.475 0.791 2.216 0.896 0.973 1.396 0.949 1.379 1.407 0.859 1.268\n", "  1.281 1.454 0.81  1.143 0.667]\n", " [1.811 1.269 3.142 2.387 2.46  2.122 1.937 2.044 1.987 4.407 1.789 2.12\n", "  1.593 1.982 0.945 3.96  0.956 1.094 1.758 1.157 1.788 1.692 0.905 1.879\n", "  1.712 2.    0.945 1.421 0.713]\n", " [1.303 1.068 1.932 1.773 1.577 1.624 1.367 1.428 1.454 1.789 2.373 1.51\n", "  1.166 1.501 0.756 1.941 0.824 0.998 1.239 0.887 1.366 1.414 0.797 1.299\n", "  1.296 1.41  0.764 1.071 0.783]\n", " [1.432 1.089 2.283 2.319 2.238 1.771 1.62  1.783 1.748 2.12  1.51  2.516\n", "  1.326 1.575 0.889 2.345 0.958 1.022 1.623 1.02  1.489 1.532 0.848 1.377\n", "  1.444 1.547 0.81  1.211 0.63 ]\n", " [1.218 0.899 1.56  1.571 1.513 1.441 1.399 1.308 1.217 1.593 1.166 1.326\n", "  2.052 1.399 0.727 1.749 0.786 0.795 1.154 0.829 1.296 1.12  0.743 1.105\n", "  1.088 1.214 0.739 0.998 0.598]\n", " [1.674 1.218 2.012 1.797 1.929 1.939 1.843 1.533 1.475 1.982 1.501 1.575\n", "  1.399 3.289 0.853 2.112 0.85  0.89  1.412 1.002 1.9   1.352 0.842 1.317\n", "  1.334 1.487 0.847 1.165 0.766]\n", " [0.74  0.926 0.993 0.968 0.925 0.846 0.894 0.878 0.791 0.945 0.756 0.889\n", "  0.727 0.853 1.153 1.027 0.642 0.59  0.848 0.892 0.825 0.748 0.694 0.761\n", "  0.929 0.819 0.61  0.806 0.547]\n", " [1.839 1.391 3.707 2.597 2.64  2.191 2.057 2.279 2.216 3.96  1.941 2.345\n", "  1.749 2.112 1.027 5.271 1.08  1.235 1.892 1.297 1.91  1.85  1.068 2.164\n", "  1.85  2.169 1.112 1.555 0.779]\n", " [0.719 0.682 1.094 1.144 0.947 0.837 0.794 0.938 0.896 0.956 0.824 0.958\n", "  0.786 0.85  0.642 1.08  1.264 0.679 0.804 0.74  0.819 0.845 0.749 0.891\n", "  0.849 0.794 0.633 0.719 0.514]\n", " [0.884 0.727 1.319 1.298 0.971 1.075 0.905 1.092 0.973 1.094 0.998 1.022\n", "  0.795 0.89  0.59  1.235 0.679 1.518 0.816 0.719 0.943 1.027 0.615 1.\n", "  0.947 0.994 0.533 0.673 0.504]\n", " [1.241 1.025 1.845 1.643 1.894 1.475 1.438 1.385 1.396 1.758 1.239 1.623\n", "  1.154 1.412 0.848 1.892 0.804 0.816 2.028 0.9   1.265 1.243 0.787 1.194\n", "  1.193 1.282 0.752 1.099 0.622]\n", " [0.823 1.156 1.236 1.071 1.089 1.041 1.014 1.078 0.949 1.157 0.887 1.02\n", "  0.829 1.002 0.892 1.297 0.74  0.719 0.9   2.007 0.952 0.849 0.732 1.008\n", "  1.15  0.933 0.722 0.897 0.614]\n", " [1.561 1.166 1.899 1.615 1.711 1.978 1.72  1.429 1.379 1.788 1.366 1.489\n", "  1.296 1.9   0.825 1.91  0.819 0.943 1.265 0.952 2.759 1.308 0.832 1.214\n", "  1.285 1.493 0.793 1.113 0.705]\n", " [1.324 0.984 1.894 1.775 1.642 1.768 1.382 1.314 1.407 1.692 1.414 1.532\n", "  1.12  1.352 0.748 1.85  0.845 1.027 1.243 0.849 1.308 2.864 0.751 1.153\n", "  1.26  1.411 0.71  1.046 0.651]\n", " [0.752 0.798 1.041 0.91  0.865 0.784 0.865 0.831 0.859 0.905 0.797 0.848\n", "  0.743 0.842 0.694 1.068 0.749 0.615 0.787 0.732 0.832 0.751 1.289 0.806\n", "  0.766 0.763 0.663 0.797 0.645]\n", " [1.027 0.956 1.921 1.666 1.456 1.328 1.206 1.459 1.268 1.879 1.299 1.377\n", "  1.105 1.317 0.761 2.164 0.891 1.    1.194 1.008 1.214 1.153 0.806 2.27\n", "  1.259 1.294 0.812 0.986 0.676]\n", " [1.298 1.259 1.823 1.707 1.478 1.365 1.273 1.466 1.281 1.712 1.296 1.444\n", "  1.088 1.334 0.929 1.85  0.849 0.947 1.193 1.15  1.285 1.26  0.766 1.259\n", "  3.352 1.267 0.697 1.137 0.685]\n", " [1.466 1.111 2.314 1.784 1.687 1.912 1.488 1.48  1.454 2.    1.41  1.547\n", "  1.214 1.487 0.819 2.169 0.794 0.994 1.282 0.933 1.493 1.411 0.763 1.294\n", "  1.267 2.982 0.709 1.007 0.656]\n", " [0.657 0.688 0.986 0.82  0.92  0.787 0.811 0.83  0.81  0.945 0.764 0.81\n", "  0.739 0.847 0.61  1.112 0.633 0.533 0.752 0.722 0.793 0.71  0.663 0.812\n", "  0.697 0.709 1.371 0.697 0.561]\n", " [1.078 1.091 1.421 1.345 1.326 1.28  1.173 1.042 1.143 1.421 1.071 1.211\n", "  0.998 1.165 0.806 1.555 0.719 0.673 1.099 0.897 1.113 1.046 0.797 0.986\n", "  1.137 1.007 0.697 3.073 0.759]\n", " [0.631 0.682 0.707 0.647 0.697 0.666 0.753 0.567 0.667 0.713 0.783 0.63\n", "  0.598 0.766 0.547 0.779 0.514 0.504 0.622 0.614 0.705 0.651 0.645 0.676\n", "  0.685 0.656 0.561 0.759 1.452]]\n"]}], "source": ["#compute asset returns\n", "arStockPrices = np.asarray(StockData)\n", "[Rows, Cols]=arStockPrices.shape\n", "arReturns = StockReturnsComputing(arStockPrices, Rows, Cols)\n", "\n", "#compute mean returns and variance covariance matrix of returns\n", "meanReturns = np.mean(arReturns, axis = 0)\n", "covReturns = np.cov(arReturns, rowvar=False)\n", " \n", "#set precision for printing results\n", "np.set_printoptions(precision=3, suppress = True)\n", "\n", "#display mean returns and variance-covariance matrix of returns\n", "print('Mean returns of assets in k-portfolio 1\\n', meanReturns)\n", "print('Variance-Covariance matrix of returns\\n', covReturns)"]}, {"cell_type": "markdown", "source": ["### Use PyPortfolioOpt"], "metadata": {"id": "zC7r-cI8RR1X"}}, {"cell_type": "code", "source": ["from pypfopt.efficient_frontier import EfficientFrontier\n", "\n", "ef_mean = EfficientFrontier(meanReturns, covReturns, weight_bounds=(0, 0.5))\n", "raw_weights_mean = ef_mean.max_sharpe()\n", "cleaned_weights_mean = ef_mean.clean_weights()\n", "mvo_weights = np.array([1000000 * cleaned_weights_mean[i] for i in range(29)])\n", "mvo_weights"], "metadata": {"id": "b1btTONEdCU4", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "75096462-7dfb-4ce6-c6f4-4671f11e79fc"}, "execution_count": 19, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([424250.,      0.,      0.,      0.,      0., 108650.,      0.,\n", "            0.,      0.,      0., 181450.,      0.,      0.,      0.,\n", "            0.,      0.,      0.,      0.,      0.,      0.,  16960.,\n", "            0.,      0.,      0., 133540., 135150.,      0.,      0.,\n", "            0.])"]}, "metadata": {}, "execution_count": 19}]}, {"cell_type": "code", "source": ["LastPrice = np.array([1/p for p in StockData.tail(1).to_numpy()[0]])\n", "Initial_Portfolio = np.multiply(mvo_weights, LastPrice)\n", "Initial_Portfolio"], "metadata": {"id": "F38NJRJJgOmj", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "f575651b-1e9b-4015-ae71-c9fc2c3a3dae"}, "execution_count": 20, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([4731.545,    0.   ,    0.   ,    0.   ,    0.   ,  579.993,\n", "          0.   ,    0.   ,    0.   ,    0.   ,  766.21 ,    0.   ,\n", "          0.   ,    0.   ,    0.   ,    0.   ,    0.   ,    0.   ,\n", "          0.   ,    0.   ,   85.465,    0.   ,    0.   ,    0.   ,\n", "        468.596,  712.801,    0.   ,    0.   ,    0.   ])"]}, "metadata": {}, "execution_count": 20}]}, {"cell_type": "code", "source": ["Portfolio_Assets = TradeData @ Initial_Portfolio\n", "MVO_result = pd.DataFrame(Portfolio_Assets, columns=[\"Mean Var\"])\n", "# MVO_result"], "metadata": {"id": "ZAd1iXqZhQ6X"}, "execution_count": 42, "outputs": []}, {"cell_type": "markdown", "source": ["# Part 4: DJIA index"], "metadata": {"id": "I5sgGe7g1HsL"}}, {"cell_type": "markdown", "source": ["Add DJIA index as a baseline to compare with."], "metadata": {"id": "sVe_ufxTY2CW"}}, {"cell_type": "code", "source": ["TRAIN_START_DATE = '2009-01-01'\n", "TRAIN_END_DATE = '2020-07-01'\n", "TRADE_START_DATE = '2020-07-01'\n", "TRADE_END_DATE = '2021-10-29'"], "metadata": {"id": "sACPzsI-6k8q"}, "execution_count": 22, "outputs": []}, {"cell_type": "code", "source": ["df_dji = YahooDownloader(start_date = TRADE_START_DATE,\n", "                     end_date = TRADE_END_DATE,\n", "                     ticker_list = ['dji']).fetch_data()\n", "# df_dji"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TuszW-OB1K0m", "outputId": "b89a8350-de58-4fea-8e4b-856efa872712"}, "execution_count": 41, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\r[*********************100%***********************]  1 of 1 completed\n", "Shape of DataFrame:  (319, 8)\n"]}]}, {"cell_type": "code", "source": ["df_dji = df_dji[['date','close']]\n", "fst_day = df_dji['close'][0]\n", "dji = pd.merge(df_dji['date'], df_dji['close'].div(fst_day).mul(1000000), \n", "               how='outer', left_index=True, right_index=True).set_index('date')\n", "# dji"], "metadata": {"id": "Q3RXz72U1VbV"}, "execution_count": 25, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "W6vvNSC6h1jZ"}, "source": ["<a id='4'></a>\n", "# Part 5: Backtesting Results\n", "Backtesting plays a key role in evaluating the performance of a trading strategy. Automated backtesting tool is preferred because it reduces the human error. We usually use the Quantopian pyfolio package to backtest our trading strategies. It is easy to use and consists of various individual plots that provide a comprehensive image of the performance of a trading strategy."]}, {"cell_type": "code", "source": ["df_result_a2c = df_account_value_a2c.set_index(df_account_value_a2c.columns[0]) if if_using_a2c else None\n", "df_result_ddpg = df_account_value_ddpg.set_index(df_account_value_ddpg.columns[0]) if if_using_ddpg else None\n", "df_result_ppo = df_account_value_ppo.set_index(df_account_value_ppo.columns[0]) if if_using_ppo else None\n", "df_result_td3 = df_account_value_td3.set_index(df_account_value_td3.columns[0]) if if_using_td3 else None\n", "df_result_sac = df_account_value_sac.set_index(df_account_value_sac.columns[0]) if if_using_sac else None\n", "\n", "result = pd.DataFrame()\n", "if if_using_a2c: result = pd.merge(result, df_result_a2c, how='outer', left_index=True, right_index=True)\n", "if if_using_ddpg: result = pd.merge(result, df_result_ddpg, how='outer', left_index=True, right_index=True)\n", "if if_using_ppo: result = pd.merge(result, df_result_ppo, how='outer', left_index=True, right_index=True)\n", "if if_using_td3: result = pd.merge(result, df_result_td3, how='outer', left_index=True, right_index=True)\n", "if if_using_sac: result = pd.merge(result, df_result_sac, how='outer', left_index=True, right_index=True)\n", "result = pd.merge(result, MVO_result, how='outer', left_index=True, right_index=True)\n", "result = pd.merge(result, dji, how='outer', left_index=True, right_index=True).fillna(method='bfill')"], "metadata": {"id": "KeDeGAc9VrEg"}, "execution_count": 45, "outputs": []}, {"cell_type": "code", "source": ["col_name = []\n", "col_name.append('A2C') if if_using_a2c else None\n", "col_name.append('DDPG') if if_using_ddpg else None\n", "col_name.append('PPO') if if_using_ppo else None\n", "col_name.append('TD3') if if_using_td3 else None\n", "col_name.append('SAC') if if_using_sac else None\n", "col_name.append('Mean Var')\n", "col_name.append('djia') \n", "result.columns = col_name"], "metadata": {"id": "JvlTVQwsiMyx"}, "execution_count": 46, "outputs": []}, {"cell_type": "code", "source": ["result"], "metadata": {"id": "l4FZxyDt3XaE", "colab": {"base_uri": "https://localhost:8080/", "height": 455}, "outputId": "2e739637-bf88-4698-9cf1-9a526452e465"}, "execution_count": 47, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["                     A2C      Mean Var          djia\n", "date                                                \n", "2020-07-01  1.000000e+06  1.001918e+06  1.000000e+06\n", "2020-07-02  1.000939e+06  1.004235e+06  1.021449e+06\n", "2020-07-06  1.006878e+06  1.023225e+06  1.021449e+06\n", "2020-07-07  9.960382e+05  1.014021e+06  1.006031e+06\n", "2020-07-08  1.001450e+06  1.029461e+06  1.012912e+06\n", "...                  ...           ...           ...\n", "2021-10-22  1.860643e+06  1.535668e+06  1.386322e+06\n", "2021-10-25  1.862007e+06  1.542078e+06  1.388813e+06\n", "2021-10-26  1.871592e+06  1.545514e+06  1.389427e+06\n", "2021-10-27  1.844267e+06  1.534916e+06  1.379083e+06\n", "2021-10-28           NaN           NaN  1.388401e+06\n", "\n", "[336 rows x 3 columns]"], "text/html": ["\n", "  <div id=\"df-acad41ed-05ef-44f7-b61c-d77b702c0582\">\n", "    <div class=\"colab-df-container\">\n", "      <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A2C</th>\n", "      <th>Mean Var</th>\n", "      <th>djia</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-07-01</th>\n", "      <td>1.000000e+06</td>\n", "      <td>1.001918e+06</td>\n", "      <td>1.000000e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-02</th>\n", "      <td>1.000939e+06</td>\n", "      <td>1.004235e+06</td>\n", "      <td>1.021449e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-06</th>\n", "      <td>1.006878e+06</td>\n", "      <td>1.023225e+06</td>\n", "      <td>1.021449e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-07</th>\n", "      <td>9.960382e+05</td>\n", "      <td>1.014021e+06</td>\n", "      <td>1.006031e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-08</th>\n", "      <td>1.001450e+06</td>\n", "      <td>1.029461e+06</td>\n", "      <td>1.012912e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-22</th>\n", "      <td>1.860643e+06</td>\n", "      <td>1.535668e+06</td>\n", "      <td>1.386322e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-25</th>\n", "      <td>1.862007e+06</td>\n", "      <td>1.542078e+06</td>\n", "      <td>1.388813e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-26</th>\n", "      <td>1.871592e+06</td>\n", "      <td>1.545514e+06</td>\n", "      <td>1.389427e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-27</th>\n", "      <td>1.844267e+06</td>\n", "      <td>1.534916e+06</td>\n", "      <td>1.379083e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-28</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.388401e+06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>336 rows × 3 columns</p>\n", "</div>\n", "      <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-acad41ed-05ef-44f7-b61c-d77b702c0582')\"\n", "              title=\"Convert this dataframe to an interactive table.\"\n", "              style=\"display:none;\">\n", "        \n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n", "    <path d=\"M18.56 5.44l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94zm-11 1L8.5 8.5l.94-2.06 2.06-.94-2.06-.94L8.5 2.5l-.94 2.06-2.06.94zm10 10l.94 2.06.94-2.06 2.06-.94-2.06-.94-.94-2.06-.94 2.06-2.06.94z\"/><path d=\"M17.41 7.96l-1.37-1.37c-.4-.4-.92-.59-1.43-.59-.52 0-1.04.2-1.43.59L10.3 9.45l-7.72 7.72c-.78.78-.78 2.05 0 2.83L4 21.41c.39.39.9.59 1.41.59.51 0 1.02-.2 1.41-.59l7.78-7.78 2.81-2.81c.8-.78.8-2.07 0-2.86zM5.41 20L4 18.59l7.72-7.72 1.47 1.35L5.41 20z\"/>\n", "  </svg>\n", "      </button>\n", "      \n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      flex-wrap:wrap;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "      <script>\n", "        const buttonEl =\n", "          document.querySelector('#df-acad41ed-05ef-44f7-b61c-d77b702c0582 button.colab-df-convert');\n", "        buttonEl.style.display =\n", "          google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "        async function convertToInteractive(key) {\n", "          const element = document.querySelector('#df-acad41ed-05ef-44f7-b61c-d77b702c0582');\n", "          const dataTable =\n", "            await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                     [key], {});\n", "          if (!dataTable) return;\n", "\n", "          const docLinkHtml = 'Like what you see? Visit the ' +\n", "            '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "            + ' to learn more about interactive tables.';\n", "          element.innerHTML = '';\n", "          dataTable['output_type'] = 'display_data';\n", "          await google.colab.output.renderOutput(dataTable, element);\n", "          const docLink = document.createElement('div');\n", "          docLink.innerHTML = docLinkHtml;\n", "          element.appendChild(docLink);\n", "        }\n", "      </script>\n", "    </div>\n", "  </div>\n", "  "]}, "metadata": {}, "execution_count": 47}]}, {"cell_type": "markdown", "source": ["Now, everything is ready, we can plot the backtest result."], "metadata": {"id": "QQuc5hI9Yklt"}}, {"cell_type": "code", "source": ["plt.rcParams[\"figure.figsize\"] = (15,5)\n", "plt.figure()\n", "result.plot()"], "metadata": {"id": "6xRfrqK4RVfq", "colab": {"base_uri": "https://localhost:8080/", "height": 381}, "outputId": "469c9729-fd57-417c-9b13-2243426923e2"}, "execution_count": 48, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<AxesSubplot:xlabel='date'>"]}, "metadata": {}, "execution_count": 48}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1080x360 with 0 Axes>"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1080x360 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}]}