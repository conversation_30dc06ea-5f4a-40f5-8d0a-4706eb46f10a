{"cells": [{"cell_type": "markdown", "metadata": {"id": "yfv52r2G33jY"}, "source": ["<a href=\"https://colab.research.google.com/github/AI4Finance-Foundation/FinRL-Tutorials/blob/master/1-Introduction/Stock_NeurIPS2018_call_func_SB3.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "gXaoZs2lh1hi"}, "source": ["# Deep Reinforcement Learning for Stock Trading from Scratch: Multiple Stock Trading\n", "\n", "* **Pytorch Version** \n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "lGunVt8oLCVS"}, "source": ["# Content"]}, {"cell_type": "markdown", "metadata": {"id": "sApkDlD9LIZv"}, "source": ["<a id='0'></a>\n", "Task Discription"]}, {"cell_type": "markdown", "metadata": {"id": "HjLD2TZSLKZ-"}, "source": ["We train a DRL agent for stock trading. This task is modeled as a Markov Decision Process (MDP), and the objective function is maximizing (expected) cumulative return.\n", "\n", "We specify the state-action-reward as follows:\n", "\n", "* **State s**: The state space represents an agent's perception of the market environment. Just like a human trader analyzing various information, here our agent passively observes many features and learns by interacting with the market environment (usually by replaying historical data).\n", "\n", "* **Action a**: The action space includes allowed actions that an agent can take at each state. For example, a ∈ {−1, 0, 1}, where −1, 0, 1 represent\n", "selling, holding, and buying. When an action operates multiple shares, a ∈{−k, ..., −1, 0, 1, ..., k}, e.g.. \"Buy\n", "10 shares of AAPL\" or \"Sell 10 shares of AAPL\" are 10 or −10, respectively\n", "\n", "* **Reward function r(s, a, s′)**: Reward is an incentive for an agent to learn a better policy. For example, it can be the change of the portfolio value when taking a at state s and arriving at new state s',  i.e., r(s, a, s′) = v′ − v, where v′ and v represent the portfolio values at state s′ and s, respectively\n", "\n", "\n", "**Market environment**: 30 consituent stocks of Dow Jones Industrial Average (DJIA) index. Accessed at the starting date of the testing period.\n", "\n", "\n", "The data for this case study is obtained from Yahoo Finance API. The data contains Open-High-Low-Close price and volume.\n"]}, {"cell_type": "markdown", "metadata": {"id": "Ffsre789LY08"}, "source": ["<a id='1'></a>\n", "# Part 1. Install Python Packages"]}, {"cell_type": "markdown", "metadata": {"id": "Uy5_PTmOh1hj"}, "source": ["<a id='1.1'></a>\n", "## 1.1. Install packages\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mPT0ipYE28wL", "pycharm": {"is_executing": true}}, "outputs": [], "source": ["## install required packages\n", "\n", "!pip install swig\n", "!pip install wrds\n", "!pip install pyportfolioopt\n", "## install finrl library\n", "!pip install -q condacolab\n", "import condacolab\n", "condacolab.install()\n", "!apt-get update -y -qq && apt-get install -y -qq cmake libopenmpi-dev python3-dev zlib1g-dev libgl1-mesa-glx swig\n", "!pip install git+https://github.com/AI4Finance-Foundation/FinRL.git"]}, {"cell_type": "markdown", "metadata": {"id": "nGv01K8Sh1hn"}, "source": ["<a id='1.3'></a>\n", "## 1.2. Import Packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lPqeTTwoh1hn", "outputId": "7918ded5-5571-4aa0-c335-e5ff1ba5a94e"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.9/site-packages/pyfolio/pos.py:26: UserWarning: Module \"zipline.assets\" not found; multipliers will not be applied to position notionals.\n", "  warnings.warn(\n"]}], "source": ["\n", "\n", "from finrl import config\n", "from finrl import config_tickers\n", "from finrl.agents.stablebaselines3.models import DRLAgent\n", "from finrl.config import DATA_SAVE_DIR\n", "from finrl.config import INDICATORS\n", "from finrl.config import RESULTS_DIR\n", "from finrl.config import TENSORBOARD_LOG_DIR\n", "from finrl.config import TEST_END_DATE\n", "from finrl.config import TEST_START_DATE\n", "from finrl.config import TRAINED_MODEL_DIR\n", "from finrl.config_tickers import DOW_30_TICKER\n", "from finrl.main import check_and_make_directories\n", "from finrl.meta.data_processor import DataProcessor\n", "from finrl.meta.data_processors.func import calc_train_trade_data\n", "from finrl.meta.data_processors.func import calc_train_trade_starts_ends_if_rolling\n", "from finrl.meta.data_processors.func import date2str\n", "from finrl.meta.data_processors.func import str2date\n", "from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv\n", "from finrl.meta.preprocessor.preprocessors import data_split\n", "from finrl.meta.preprocessor.preprocessors import FeatureEngineer\n", "from finrl.meta.preprocessor.yahoodownloader import YahooDownloader\n", "from finrl.plot import backtest_plot\n", "from finrl.plot import backtest_stats\n", "from finrl.plot import get_baseline\n", "from finrl.plot import get_daily_return\n", "from finrl.plot import plot_return\n", "from finrl.applications.stock_trading.stock_trading import stock_trading\n", "import sys\n", "sys.path.append(\"../FinRL\")\n", "\n", "import itertools"]}, {"cell_type": "markdown", "metadata": {"id": "T2owTj985RW4"}, "source": ["<a id='1.4'></a>\n", "# 2 Set parameters and run\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "RtUc_ofKmpdy", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "203fec48-d3fa-48fe-ec40-eda9a2799c48"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[1;30;43m流式输出内容被截断，只能显示最后 5000 行内容。\u001b[0m\n", "|    std                  | 1.02        |\n", "|    value_loss           | 52.5        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 10          |\n", "|    time_elapsed         | 316         |\n", "|    total_timesteps      | 20480       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.018726377 |\n", "|    clip_fraction        | 0.228       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.6       |\n", "|    explained_variance   | -0.00599    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 10          |\n", "|    n_updates            | 90          |\n", "|    policy_gradient_loss | -0.0229     |\n", "|    reward               | 1.8604985   |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 34          |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 11          |\n", "|    time_elapsed         | 350         |\n", "|    total_timesteps      | 22528       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.017771121 |\n", "|    clip_fraction        | 0.201       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.7       |\n", "|    explained_variance   | -0.00452    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 102         |\n", "|    n_updates            | 100         |\n", "|    policy_gradient_loss | -0.0176     |\n", "|    reward               | 2.4363315   |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 257         |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 12          |\n", "|    time_elapsed         | 380         |\n", "|    total_timesteps      | 24576       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.021592125 |\n", "|    clip_fraction        | 0.24        |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.7       |\n", "|    explained_variance   | -0.00462    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 13.1        |\n", "|    n_updates            | 110         |\n", "|    policy_gradient_loss | -0.0218     |\n", "|    reward               | -0.36686477 |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 27.8        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 13          |\n", "|    time_elapsed         | 417         |\n", "|    total_timesteps      | 26624       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.016095877 |\n", "|    clip_fraction        | 0.171       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.8       |\n", "|    explained_variance   | 0.00607     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 63.8        |\n", "|    n_updates            | 120         |\n", "|    policy_gradient_loss | -0.0175     |\n", "|    reward               | -6.2590113  |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 161         |\n", "-----------------------------------------\n", "----------------------------------------\n", "| time/                   |            |\n", "|    fps                  | 64         |\n", "|    iterations           | 14         |\n", "|    time_elapsed         | 447        |\n", "|    total_timesteps      | 28672      |\n", "| train/                  |            |\n", "|    approx_kl            | 0.02099569 |\n", "|    clip_fraction        | 0.204      |\n", "|    clip_range           | 0.2        |\n", "|    entropy_loss         | -41.9      |\n", "|    explained_variance   | 0.00587    |\n", "|    learning_rate        | 0.00025    |\n", "|    loss                 | 18.1       |\n", "|    n_updates            | 130        |\n", "|    policy_gradient_loss | -0.0176    |\n", "|    reward               | -1.5635415 |\n", "|    std                  | 1.03       |\n", "|    value_loss           | 76.1       |\n", "----------------------------------------\n", "day: 3374, episode: 10\n", "begin_total_asset: 1017321.61\n", "end_total_asset: 4690150.25\n", "total_reward: 3672828.63\n", "total_cost: 440655.06\n", "total_trades: 91574\n", "Sharpe: 0.777\n", "=================================\n", "------------------------------------------\n", "| time/                   |              |\n", "|    fps                  | 63           |\n", "|    iterations           | 15           |\n", "|    time_elapsed         | 480          |\n", "|    total_timesteps      | 30720        |\n", "| train/                  |              |\n", "|    approx_kl            | 0.01574407   |\n", "|    clip_fraction        | 0.252        |\n", "|    clip_range           | 0.2          |\n", "|    entropy_loss         | -41.9        |\n", "|    explained_variance   | 0.045        |\n", "|    learning_rate        | 0.00025      |\n", "|    loss                 | 8.21         |\n", "|    n_updates            | 140          |\n", "|    policy_gradient_loss | -0.0207      |\n", "|    reward               | -0.058135245 |\n", "|    std                  | 1.03         |\n", "|    value_loss           | 20           |\n", "------------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 16          |\n", "|    time_elapsed         | 511         |\n", "|    total_timesteps      | 32768       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.018864237 |\n", "|    clip_fraction        | 0.19        |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42         |\n", "|    explained_variance   | -0.0334     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 40.5        |\n", "|    n_updates            | 150         |\n", "|    policy_gradient_loss | -0.0158     |\n", "|    reward               | 2.1892703   |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 80.4        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 17          |\n", "|    time_elapsed         | 542         |\n", "|    total_timesteps      | 34816       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.025924759 |\n", "|    clip_fraction        | 0.183       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42         |\n", "|    explained_variance   | -0.0494     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 8.64        |\n", "|    n_updates            | 160         |\n", "|    policy_gradient_loss | -0.0154     |\n", "|    reward               | -1.6194284  |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 19.1        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 18          |\n", "|    time_elapsed         | 576         |\n", "|    total_timesteps      | 36864       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.023486339 |\n", "|    clip_fraction        | 0.227       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42         |\n", "|    explained_variance   | -0.00164    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 71          |\n", "|    n_updates            | 170         |\n", "|    policy_gradient_loss | -0.0128     |\n", "|    reward               | -6.5787015  |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 175         |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 19          |\n", "|    time_elapsed         | 609         |\n", "|    total_timesteps      | 38912       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.047546946 |\n", "|    clip_fraction        | 0.278       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42         |\n", "|    explained_variance   | 0.0083      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 22.2        |\n", "|    n_updates            | 180         |\n", "|    policy_gradient_loss | -0.00743    |\n", "|    reward               | 3.6853487   |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 88.3        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 20          |\n", "|    time_elapsed         | 643         |\n", "|    total_timesteps      | 40960       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.028585846 |\n", "|    clip_fraction        | 0.238       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.1       |\n", "|    explained_variance   | -0.018      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 12.6        |\n", "|    n_updates            | 190         |\n", "|    policy_gradient_loss | -0.0166     |\n", "|    reward               | 2.84366     |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 35.7        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 21          |\n", "|    time_elapsed         | 672         |\n", "|    total_timesteps      | 43008       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.021615773 |\n", "|    clip_fraction        | 0.283       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.1       |\n", "|    explained_variance   | 0.0164      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 39.1        |\n", "|    n_updates            | 200         |\n", "|    policy_gradient_loss | -0.0119     |\n", "|    reward               | 7.260352    |\n", "|    std                  | 1.04        |\n", "|    value_loss           | 85.5        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 22          |\n", "|    time_elapsed         | 703         |\n", "|    total_timesteps      | 45056       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.023984132 |\n", "|    clip_fraction        | 0.174       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.2       |\n", "|    explained_variance   | -0.0214     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 10.8        |\n", "|    n_updates            | 210         |\n", "|    policy_gradient_loss | -0.015      |\n", "|    reward               | 0.7453349   |\n", "|    std                  | 1.04        |\n", "|    value_loss           | 27.4        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 23          |\n", "|    time_elapsed         | 736         |\n", "|    total_timesteps      | 47104       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.026311198 |\n", "|    clip_fraction        | 0.239       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.2       |\n", "|    explained_variance   | 0.0117      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 53.5        |\n", "|    n_updates            | 220         |\n", "|    policy_gradient_loss | -0.0147     |\n", "|    reward               | -3.601917   |\n", "|    std                  | 1.04        |\n", "|    value_loss           | 109         |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 24          |\n", "|    time_elapsed         | 765         |\n", "|    total_timesteps      | 49152       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.021329464 |\n", "|    clip_fraction        | 0.228       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.2       |\n", "|    explained_variance   | 0.0287      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 35.5        |\n", "|    n_updates            | 230         |\n", "|    policy_gradient_loss | -0.0174     |\n", "|    reward               | -1.4932549  |\n", "|    std                  | 1.04        |\n", "|    value_loss           | 69.7        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 25          |\n", "|    time_elapsed         | 799         |\n", "|    total_timesteps      | 51200       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.033834375 |\n", "|    clip_fraction        | 0.347       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.2       |\n", "|    explained_variance   | -0.0439     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 11.2        |\n", "|    n_updates            | 240         |\n", "|    policy_gradient_loss | -0.0175     |\n", "|    reward               | -0.13293022 |\n", "|    std                  | 1.04        |\n", "|    value_loss           | 31.1        |\n", "-----------------------------------------\n", "{'batch_size': 128, 'buffer_size': 100000, 'learning_rate': 0.0001, 'learning_starts': 100, 'ent_coef': 'auto_0.1'}\n", "Using cpu device\n", "Logging to results/sac\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 4          |\n", "|    fps             | 19         |\n", "|    time_elapsed    | 693        |\n", "|    total_timesteps | 13500      |\n", "| train/             |            |\n", "|    actor_loss      | 1.23e+03   |\n", "|    critic_loss     | 941        |\n", "|    ent_coef        | 0.175      |\n", "|    ent_coef_loss   | -80.9      |\n", "|    learning_rate   | 0.0001     |\n", "|    n_updates       | 13399      |\n", "|    reward          | -4.1185117 |\n", "-----------------------------------\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 8          |\n", "|    fps             | 19         |\n", "|    time_elapsed    | 1407       |\n", "|    total_timesteps | 27000      |\n", "| train/             |            |\n", "|    actor_loss      | 486        |\n", "|    critic_loss     | 378        |\n", "|    ent_coef        | 0.047      |\n", "|    ent_coef_loss   | -97.4      |\n", "|    learning_rate   | 0.0001     |\n", "|    n_updates       | 26899      |\n", "|    reward          | -6.0287046 |\n", "-----------------------------------\n", "day: 3374, episode: 10\n", "begin_total_asset: 1039580.61\n", "end_total_asset: 4449383.64\n", "total_reward: 3409803.03\n", "total_cost: 3171.06\n", "total_trades: 48397\n", "Sharpe: 0.687\n", "=================================\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 12         |\n", "|    fps             | 19         |\n", "|    time_elapsed    | 2123       |\n", "|    total_timesteps | 40500      |\n", "| train/             |            |\n", "|    actor_loss      | 201        |\n", "|    critic_loss     | 10.8       |\n", "|    ent_coef        | 0.0131     |\n", "|    ent_coef_loss   | -63.2      |\n", "|    learning_rate   | 0.0001     |\n", "|    n_updates       | 40399      |\n", "|    reward          | -5.6925883 |\n", "-----------------------------------\n", "{'batch_size': 100, 'buffer_size': 1000000, 'learning_rate': 0.001}\n", "Using cpu device\n", "Logging to results/td3\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 4          |\n", "|    fps             | 24         |\n", "|    time_elapsed    | 545        |\n", "|    total_timesteps | 13500      |\n", "| train/             |            |\n", "|    actor_loss      | 16.7       |\n", "|    critic_loss     | 341        |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 10125      |\n", "|    reward          | -5.7216434 |\n", "-----------------------------------\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 8          |\n", "|    fps             | 21         |\n", "|    time_elapsed    | 1228       |\n", "|    total_timesteps | 27000      |\n", "| train/             |            |\n", "|    actor_loss      | 18.7       |\n", "|    critic_loss     | 21.4       |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 23625      |\n", "|    reward          | -5.7216434 |\n", "-----------------------------------\n", "day: 3374, episode: 10\n", "begin_total_asset: 1043903.24\n", "end_total_asset: 5291054.90\n", "total_reward: 4247151.66\n", "total_cost: 1042.86\n", "total_trades: 64106\n", "Sharpe: 0.723\n", "=================================\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 12         |\n", "|    fps             | 21         |\n", "|    time_elapsed    | 1923       |\n", "|    total_timesteps | 40500      |\n", "| train/             |            |\n", "|    actor_loss      | 20.6       |\n", "|    critic_loss     | 15.9       |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 37125      |\n", "|    reward          | -5.7216434 |\n", "-----------------------------------\n", "hit end!\n", "hit end!\n", "hit end!\n", "hit end!\n", "hit end!\n", "[*********************100%***********************]  1 of 1 completed\n", "Shape of DataFrame:  (22, 8)\n", "i:  2\n", "{'n_steps': 5, 'ent_coef': 0.01, 'learning_rate': 0.0007}\n", "Using cpu device\n", "Logging to results/a2c\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 55          |\n", "|    iterations         | 100         |\n", "|    time_elapsed       | 9           |\n", "|    total_timesteps    | 500         |\n", "| train/                |             |\n", "|    entropy_loss       | -41         |\n", "|    explained_variance | 0.0552      |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 99          |\n", "|    policy_loss        | -125        |\n", "|    reward             | -0.19224237 |\n", "|    std                | 0.997       |\n", "|    value_loss         | 10.9        |\n", "---------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 65       |\n", "|    iterations         | 200      |\n", "|    time_elapsed       | 15       |\n", "|    total_timesteps    | 1000     |\n", "| train/                |          |\n", "|    entropy_loss       | -41.1    |\n", "|    explained_variance | 0        |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 199      |\n", "|    policy_loss        | -65.7    |\n", "|    reward             | 2.47076  |\n", "|    std                | 0.998    |\n", "|    value_loss         | 3.14     |\n", "------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 300       |\n", "|    time_elapsed       | 24        |\n", "|    total_timesteps    | 1500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 299       |\n", "|    policy_loss        | 221       |\n", "|    reward             | -0.668967 |\n", "|    std                | 0.999     |\n", "|    value_loss         | 38        |\n", "-------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 61       |\n", "|    iterations         | 400      |\n", "|    time_elapsed       | 32       |\n", "|    total_timesteps    | 2000     |\n", "| train/                |          |\n", "|    entropy_loss       | -41      |\n", "|    explained_variance | 5.96e-08 |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 399      |\n", "|    policy_loss        | 2.8      |\n", "|    reward             | 2.104001 |\n", "|    std                | 0.997    |\n", "|    value_loss         | 2.71     |\n", "------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 64        |\n", "|    iterations         | 500       |\n", "|    time_elapsed       | 39        |\n", "|    total_timesteps    | 2500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | -1.19e-07 |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 499       |\n", "|    policy_loss        | 239       |\n", "|    reward             | 3.0126274 |\n", "|    std                | 0.999     |\n", "|    value_loss         | 39.6      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 600       |\n", "|    time_elapsed       | 48        |\n", "|    total_timesteps    | 3000      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.2     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 599       |\n", "|    policy_loss        | -524      |\n", "|    reward             | 3.9946847 |\n", "|    std                | 1         |\n", "|    value_loss         | 293       |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 62        |\n", "|    iterations         | 700       |\n", "|    time_elapsed       | 56        |\n", "|    total_timesteps    | 3500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.2     |\n", "|    explained_variance | 0.108     |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 699       |\n", "|    policy_loss        | -37.1     |\n", "|    reward             | 1.4987615 |\n", "|    std                | 1         |\n", "|    value_loss         | 1.8       |\n", "-------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 63       |\n", "|    iterations         | 800      |\n", "|    time_elapsed       | 62       |\n", "|    total_timesteps    | 4000     |\n", "| train/                |          |\n", "|    entropy_loss       | -41.2    |\n", "|    explained_variance | 0        |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 799      |\n", "|    policy_loss        | -337     |\n", "|    reward             | 2.046587 |\n", "|    std                | 1        |\n", "|    value_loss         | 77.1     |\n", "------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 900       |\n", "|    time_elapsed       | 72        |\n", "|    total_timesteps    | 4500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.2     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 899       |\n", "|    policy_loss        | 26.2      |\n", "|    reward             | 1.0195923 |\n", "|    std                | 1         |\n", "|    value_loss         | 4.92      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 62         |\n", "|    iterations         | 1000       |\n", "|    time_elapsed       | 79         |\n", "|    total_timesteps    | 5000       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.2      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 999        |\n", "|    policy_loss        | 80.3       |\n", "|    reward             | -3.5495179 |\n", "|    std                | 1          |\n", "|    value_loss         | 9.56       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 63        |\n", "|    iterations         | 1100      |\n", "|    time_elapsed       | 86        |\n", "|    total_timesteps    | 5500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.2     |\n", "|    explained_variance | -2.38e-07 |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 1099      |\n", "|    policy_loss        | -258      |\n", "|    reward             | 1.6695346 |\n", "|    std                | 1         |\n", "|    value_loss         | 44.9      |\n", "-------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 62       |\n", "|    iterations         | 1200     |\n", "|    time_elapsed       | 96       |\n", "|    total_timesteps    | 6000     |\n", "| train/                |          |\n", "|    entropy_loss       | -41.1    |\n", "|    explained_variance | -0.00397 |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 1199     |\n", "|    policy_loss        | 185      |\n", "|    reward             | 2.245284 |\n", "|    std                | 1        |\n", "|    value_loss         | 26.3     |\n", "------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 60        |\n", "|    iterations         | 1300      |\n", "|    time_elapsed       | 106       |\n", "|    total_timesteps    | 6500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | -1.19e-07 |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 1299      |\n", "|    policy_loss        | 150       |\n", "|    reward             | 1.491629  |\n", "|    std                | 0.998     |\n", "|    value_loss         | 21.5      |\n", "-------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 59          |\n", "|    iterations         | 1400        |\n", "|    time_elapsed       | 117         |\n", "|    total_timesteps    | 7000        |\n", "| train/                |             |\n", "|    entropy_loss       | -41.1       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 1399        |\n", "|    policy_loss        | -107        |\n", "|    reward             | -0.81370664 |\n", "|    std                | 0.998       |\n", "|    value_loss         | 8.34        |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 60        |\n", "|    iterations         | 1500      |\n", "|    time_elapsed       | 124       |\n", "|    total_timesteps    | 7500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | -0.0459   |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 1499      |\n", "|    policy_loss        | -10       |\n", "|    reward             | 2.3688922 |\n", "|    std                | 0.997     |\n", "|    value_loss         | 0.922     |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 60         |\n", "|    iterations         | 1600       |\n", "|    time_elapsed       | 131        |\n", "|    total_timesteps    | 8000       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.1      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 1599       |\n", "|    policy_loss        | 128        |\n", "|    reward             | 0.56861943 |\n", "|    std                | 0.998      |\n", "|    value_loss         | 19.5       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 1700      |\n", "|    time_elapsed       | 141       |\n", "|    total_timesteps    | 8500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41       |\n", "|    explained_variance | 0.203     |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 1699      |\n", "|    policy_loss        | 37        |\n", "|    reward             | 1.2727017 |\n", "|    std                | 0.996     |\n", "|    value_loss         | 3.2       |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 60        |\n", "|    iterations         | 1800      |\n", "|    time_elapsed       | 148       |\n", "|    total_timesteps    | 9000      |\n", "| train/                |           |\n", "|    entropy_loss       | -41       |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 1799      |\n", "|    policy_loss        | 80.9      |\n", "|    reward             | 3.9352329 |\n", "|    std                | 0.996     |\n", "|    value_loss         | 9.56      |\n", "-------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 60       |\n", "|    iterations         | 1900     |\n", "|    time_elapsed       | 156      |\n", "|    total_timesteps    | 9500     |\n", "| train/                |          |\n", "|    entropy_loss       | -41      |\n", "|    explained_variance | 0        |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 1899     |\n", "|    policy_loss        | 507      |\n", "|    reward             | 6.328624 |\n", "|    std                | 0.997    |\n", "|    value_loss         | 187      |\n", "------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 60         |\n", "|    iterations         | 2000       |\n", "|    time_elapsed       | 166        |\n", "|    total_timesteps    | 10000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.1      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 1999       |\n", "|    policy_loss        | -126       |\n", "|    reward             | -2.8187668 |\n", "|    std                | 1          |\n", "|    value_loss         | 8.37       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 60        |\n", "|    iterations         | 2100      |\n", "|    time_elapsed       | 172       |\n", "|    total_timesteps    | 10500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | -1.19e-07 |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 2099      |\n", "|    policy_loss        | -58.5     |\n", "|    reward             | 0.2734109 |\n", "|    std                | 0.999     |\n", "|    value_loss         | 3         |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 60         |\n", "|    iterations         | 2200       |\n", "|    time_elapsed       | 180        |\n", "|    total_timesteps    | 11000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.1      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 2199       |\n", "|    policy_loss        | 157        |\n", "|    reward             | 0.68144214 |\n", "|    std                | 0.997      |\n", "|    value_loss         | 19.9       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 60         |\n", "|    iterations         | 2300       |\n", "|    time_elapsed       | 190        |\n", "|    total_timesteps    | 11500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.1      |\n", "|    explained_variance | 1.19e-07   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 2299       |\n", "|    policy_loss        | -67.3      |\n", "|    reward             | -1.8721669 |\n", "|    std                | 0.999      |\n", "|    value_loss         | 2.49       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 2400       |\n", "|    time_elapsed       | 196        |\n", "|    total_timesteps    | 12000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.1      |\n", "|    explained_variance | -0.0105    |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 2399       |\n", "|    policy_loss        | 144        |\n", "|    reward             | 0.47134838 |\n", "|    std                | 0.997      |\n", "|    value_loss         | 26.3       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 60         |\n", "|    iterations         | 2500       |\n", "|    time_elapsed       | 204        |\n", "|    total_timesteps    | 12500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.1      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 2499       |\n", "|    policy_loss        | 589        |\n", "|    reward             | -1.9081986 |\n", "|    std                | 0.997      |\n", "|    value_loss         | 221        |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 60        |\n", "|    iterations         | 2600      |\n", "|    time_elapsed       | 213       |\n", "|    total_timesteps    | 13000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41       |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 2599      |\n", "|    policy_loss        | 352       |\n", "|    reward             | 6.1386447 |\n", "|    std                | 0.996     |\n", "|    value_loss         | 148       |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 2700      |\n", "|    time_elapsed       | 219       |\n", "|    total_timesteps    | 13500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41       |\n", "|    explained_variance | -0.0134   |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 2699      |\n", "|    policy_loss        | -131      |\n", "|    reward             | 0.6143146 |\n", "|    std                | 0.995     |\n", "|    value_loss         | 12.3      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 2800      |\n", "|    time_elapsed       | 229       |\n", "|    total_timesteps    | 14000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41       |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 2799      |\n", "|    policy_loss        | 132       |\n", "|    reward             | 1.7656372 |\n", "|    std                | 0.995     |\n", "|    value_loss         | 13.2      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 2900       |\n", "|    time_elapsed       | 237        |\n", "|    total_timesteps    | 14500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41        |\n", "|    explained_variance | -0.0245    |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 2899       |\n", "|    policy_loss        | 17.1       |\n", "|    reward             | 0.08867768 |\n", "|    std                | 0.995      |\n", "|    value_loss         | 3.2        |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 3000       |\n", "|    time_elapsed       | 243        |\n", "|    total_timesteps    | 15000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41        |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 2999       |\n", "|    policy_loss        | 48.4       |\n", "|    reward             | -3.6771903 |\n", "|    std                | 0.995      |\n", "|    value_loss         | 2.24       |\n", "--------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 61       |\n", "|    iterations         | 3100     |\n", "|    time_elapsed       | 253      |\n", "|    total_timesteps    | 15500    |\n", "| train/                |          |\n", "|    entropy_loss       | -41.1    |\n", "|    explained_variance | 1.19e-07 |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 3099     |\n", "|    policy_loss        | -0.565   |\n", "|    reward             | 0.679106 |\n", "|    std                | 0.998    |\n", "|    value_loss         | 1.98     |\n", "------------------------------------\n", "----------------------------------------\n", "| time/                 |              |\n", "|    fps                | 61           |\n", "|    iterations         | 3200         |\n", "|    time_elapsed       | 260          |\n", "|    total_timesteps    | 16000        |\n", "| train/                |              |\n", "|    entropy_loss       | -41.1        |\n", "|    explained_variance | 0            |\n", "|    learning_rate      | 0.0007       |\n", "|    n_updates          | 3199         |\n", "|    policy_loss        | 26.6         |\n", "|    reward             | 0.0013427841 |\n", "|    std                | 0.998        |\n", "|    value_loss         | 11.3         |\n", "----------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 3300      |\n", "|    time_elapsed       | 267       |\n", "|    total_timesteps    | 16500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 3299      |\n", "|    policy_loss        | 54.6      |\n", "|    reward             | 1.6012005 |\n", "|    std                | 0.998     |\n", "|    value_loss         | 7.47      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 3400      |\n", "|    time_elapsed       | 277       |\n", "|    total_timesteps    | 17000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | -0.0366   |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 3399      |\n", "|    policy_loss        | -33.7     |\n", "|    reward             | 0.8685799 |\n", "|    std                | 1         |\n", "|    value_loss         | 3.79      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 3500       |\n", "|    time_elapsed       | 284        |\n", "|    total_timesteps    | 17500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.1      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 3499       |\n", "|    policy_loss        | 21         |\n", "|    reward             | 0.14613488 |\n", "|    std                | 1          |\n", "|    value_loss         | 1.32       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 3600       |\n", "|    time_elapsed       | 291        |\n", "|    total_timesteps    | 18000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.1      |\n", "|    explained_variance | 5.96e-08   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 3599       |\n", "|    policy_loss        | -173       |\n", "|    reward             | -1.2669375 |\n", "|    std                | 1          |\n", "|    value_loss         | 20.9       |\n", "--------------------------------------\n", "-----------------------------------------\n", "| time/                 |               |\n", "|    fps                | 61            |\n", "|    iterations         | 3700          |\n", "|    time_elapsed       | 301           |\n", "|    total_timesteps    | 18500         |\n", "| train/                |               |\n", "|    entropy_loss       | -41.2         |\n", "|    explained_variance | 0             |\n", "|    learning_rate      | 0.0007        |\n", "|    n_updates          | 3699          |\n", "|    policy_loss        | -53.5         |\n", "|    reward             | -0.0045535835 |\n", "|    std                | 1             |\n", "|    value_loss         | 8.58          |\n", "-----------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 3800      |\n", "|    time_elapsed       | 308       |\n", "|    total_timesteps    | 19000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 3799      |\n", "|    policy_loss        | 98.2      |\n", "|    reward             | 0.6932831 |\n", "|    std                | 1         |\n", "|    value_loss         | 7.95      |\n", "-------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 61       |\n", "|    iterations         | 3900     |\n", "|    time_elapsed       | 316      |\n", "|    total_timesteps    | 19500    |\n", "| train/                |          |\n", "|    entropy_loss       | -41.1    |\n", "|    explained_variance | 0        |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 3899     |\n", "|    policy_loss        | 628      |\n", "|    reward             | 8.699067 |\n", "|    std                | 1        |\n", "|    value_loss         | 335      |\n", "------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 4000      |\n", "|    time_elapsed       | 325       |\n", "|    total_timesteps    | 20000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 3999      |\n", "|    policy_loss        | 605       |\n", "|    reward             | 17.186195 |\n", "|    std                | 1         |\n", "|    value_loss         | 266       |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 4100      |\n", "|    time_elapsed       | 331       |\n", "|    total_timesteps    | 20500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | 0.0757    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4099      |\n", "|    policy_loss        | 187       |\n", "|    reward             | 1.1687305 |\n", "|    std                | 1         |\n", "|    value_loss         | 22.2      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 4200       |\n", "|    time_elapsed       | 341        |\n", "|    total_timesteps    | 21000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.1      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 4199       |\n", "|    policy_loss        | -7.42      |\n", "|    reward             | -0.5722446 |\n", "|    std                | 1          |\n", "|    value_loss         | 0.248      |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 61          |\n", "|    iterations         | 4300        |\n", "|    time_elapsed       | 351         |\n", "|    total_timesteps    | 21500       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.2       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 4299        |\n", "|    policy_loss        | 44.7        |\n", "|    reward             | -0.39718863 |\n", "|    std                | 1           |\n", "|    value_loss         | 2.09        |\n", "---------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 61          |\n", "|    iterations         | 4400        |\n", "|    time_elapsed       | 359         |\n", "|    total_timesteps    | 22000       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.2       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 4399        |\n", "|    policy_loss        | -29.9       |\n", "|    reward             | -0.07916131 |\n", "|    std                | 1           |\n", "|    value_loss         | 0.947       |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 60        |\n", "|    iterations         | 4500      |\n", "|    time_elapsed       | 369       |\n", "|    total_timesteps    | 22500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.2     |\n", "|    explained_variance | 5.96e-08  |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4499      |\n", "|    policy_loss        | -28.5     |\n", "|    reward             | 1.8631558 |\n", "|    std                | 1         |\n", "|    value_loss         | 1.83      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 4600      |\n", "|    time_elapsed       | 375       |\n", "|    total_timesteps    | 23000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4599      |\n", "|    policy_loss        | 58.4      |\n", "|    reward             | 1.4081724 |\n", "|    std                | 0.999     |\n", "|    value_loss         | 77        |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 4700       |\n", "|    time_elapsed       | 383        |\n", "|    total_timesteps    | 23500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.1      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 4699       |\n", "|    policy_loss        | 79.9       |\n", "|    reward             | -2.0728068 |\n", "|    std                | 0.998      |\n", "|    value_loss         | 4.66       |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 61          |\n", "|    iterations         | 4800        |\n", "|    time_elapsed       | 392         |\n", "|    total_timesteps    | 24000       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.1       |\n", "|    explained_variance | 0.0901      |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 4799        |\n", "|    policy_loss        | -15.9       |\n", "|    reward             | -0.09403604 |\n", "|    std                | 0.998       |\n", "|    value_loss         | 0.165       |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 4900      |\n", "|    time_elapsed       | 399       |\n", "|    total_timesteps    | 24500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4899      |\n", "|    policy_loss        | -110      |\n", "|    reward             | 2.0542228 |\n", "|    std                | 1         |\n", "|    value_loss         | 10.8      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 5000      |\n", "|    time_elapsed       | 407       |\n", "|    total_timesteps    | 25000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4999      |\n", "|    policy_loss        | -186      |\n", "|    reward             | 2.1355224 |\n", "|    std                | 0.999     |\n", "|    value_loss         | 27.6      |\n", "-------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 61          |\n", "|    iterations         | 5100        |\n", "|    time_elapsed       | 416         |\n", "|    total_timesteps    | 25500       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.1       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 5099        |\n", "|    policy_loss        | -13.1       |\n", "|    reward             | -0.08651471 |\n", "|    std                | 0.999       |\n", "|    value_loss         | 1.59        |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 5200      |\n", "|    time_elapsed       | 423       |\n", "|    total_timesteps    | 26000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41       |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 5199      |\n", "|    policy_loss        | 99        |\n", "|    reward             | 2.3819537 |\n", "|    std                | 0.997     |\n", "|    value_loss         | 29.5      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 5300       |\n", "|    time_elapsed       | 432        |\n", "|    total_timesteps    | 26500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41        |\n", "|    explained_variance | 1.19e-07   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 5299       |\n", "|    policy_loss        | 372        |\n", "|    reward             | -22.664398 |\n", "|    std                | 0.997      |\n", "|    value_loss         | 196        |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 5400      |\n", "|    time_elapsed       | 440       |\n", "|    total_timesteps    | 27000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41       |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 5399      |\n", "|    policy_loss        | 153       |\n", "|    reward             | 1.1176498 |\n", "|    std                | 0.995     |\n", "|    value_loss         | 15.5      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 5500       |\n", "|    time_elapsed       | 446        |\n", "|    total_timesteps    | 27500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41        |\n", "|    explained_variance | -1.19e-07  |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 5499       |\n", "|    policy_loss        | -92.6      |\n", "|    reward             | -5.1304746 |\n", "|    std                | 0.996      |\n", "|    value_loss         | 14.5       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 5600      |\n", "|    time_elapsed       | 456       |\n", "|    total_timesteps    | 28000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41       |\n", "|    explained_variance | 0.0186    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 5599      |\n", "|    policy_loss        | 62.9      |\n", "|    reward             | 1.1683302 |\n", "|    std                | 0.996     |\n", "|    value_loss         | 9.46      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 5700       |\n", "|    time_elapsed       | 464        |\n", "|    total_timesteps    | 28500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41        |\n", "|    explained_variance | -1.19e-07  |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 5699       |\n", "|    policy_loss        | 34.4       |\n", "|    reward             | -3.4618378 |\n", "|    std                | 0.995      |\n", "|    value_loss         | 12         |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 5800      |\n", "|    time_elapsed       | 471       |\n", "|    total_timesteps    | 29000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41       |\n", "|    explained_variance | -0.247    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 5799      |\n", "|    policy_loss        | -171      |\n", "|    reward             | 5.8363895 |\n", "|    std                | 0.996     |\n", "|    value_loss         | 20.7      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 5900       |\n", "|    time_elapsed       | 481        |\n", "|    total_timesteps    | 29500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41        |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 5899       |\n", "|    policy_loss        | 250        |\n", "|    reward             | -4.7651134 |\n", "|    std                | 0.996      |\n", "|    value_loss         | 83.8       |\n", "--------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 61       |\n", "|    iterations         | 6000     |\n", "|    time_elapsed       | 487      |\n", "|    total_timesteps    | 30000    |\n", "| train/                |          |\n", "|    entropy_loss       | -41.1    |\n", "|    explained_variance | 0        |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 5999     |\n", "|    policy_loss        | -211     |\n", "|    reward             | 3.167639 |\n", "|    std                | 0.999    |\n", "|    value_loss         | 95.9     |\n", "------------------------------------\n", "day: 3352, episode: 10\n", "begin_total_asset: 1005653.74\n", "end_total_asset: 8785262.38\n", "total_reward: 7779608.65\n", "total_cost: 31168.83\n", "total_trades: 45054\n", "Sharpe: 0.834\n", "=================================\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 6100       |\n", "|    time_elapsed       | 495        |\n", "|    total_timesteps    | 30500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.1      |\n", "|    explained_variance | -0.113     |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 6099       |\n", "|    policy_loss        | 21.6       |\n", "|    reward             | 0.12259659 |\n", "|    std                | 1          |\n", "|    value_loss         | 0.918      |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 6200      |\n", "|    time_elapsed       | 505       |\n", "|    total_timesteps    | 31000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.1     |\n", "|    explained_variance | -0.00301  |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 6199      |\n", "|    policy_loss        | -126      |\n", "|    reward             | 1.5182142 |\n", "|    std                | 1         |\n", "|    value_loss         | 16.8      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 6300      |\n", "|    time_elapsed       | 511       |\n", "|    total_timesteps    | 31500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.2     |\n", "|    explained_variance | -1.74     |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 6299      |\n", "|    policy_loss        | -25.1     |\n", "|    reward             | 0.5284405 |\n", "|    std                | 1         |\n", "|    value_loss         | 1.56      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 6400       |\n", "|    time_elapsed       | 519        |\n", "|    total_timesteps    | 32000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.3      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 6399       |\n", "|    policy_loss        | 123        |\n", "|    reward             | -3.4704874 |\n", "|    std                | 1.01       |\n", "|    value_loss         | 14.2       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 6500      |\n", "|    time_elapsed       | 528       |\n", "|    total_timesteps    | 32500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.4     |\n", "|    explained_variance | -1.19e-07 |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 6499      |\n", "|    policy_loss        | -102      |\n", "|    reward             | 3.8022645 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 11        |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 6600      |\n", "|    time_elapsed       | 535       |\n", "|    total_timesteps    | 33000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.4     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 6599      |\n", "|    policy_loss        | 372       |\n", "|    reward             | 24.101572 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 136       |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 6700       |\n", "|    time_elapsed       | 543        |\n", "|    total_timesteps    | 33500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.4      |\n", "|    explained_variance | 5.96e-08   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 6699       |\n", "|    policy_loss        | -923       |\n", "|    reward             | -11.455252 |\n", "|    std                | 1.01       |\n", "|    value_loss         | 474        |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 6800       |\n", "|    time_elapsed       | 552        |\n", "|    total_timesteps    | 34000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.4      |\n", "|    explained_variance | -0.553     |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 6799       |\n", "|    policy_loss        | -111       |\n", "|    reward             | 0.10300914 |\n", "|    std                | 1.01       |\n", "|    value_loss         | 9.3        |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 61          |\n", "|    iterations         | 6900        |\n", "|    time_elapsed       | 558         |\n", "|    total_timesteps    | 34500       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.4       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 6899        |\n", "|    policy_loss        | 133         |\n", "|    reward             | -0.71322364 |\n", "|    std                | 1.01        |\n", "|    value_loss         | 15.3        |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 7000      |\n", "|    time_elapsed       | 567       |\n", "|    total_timesteps    | 35000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.4     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 6999      |\n", "|    policy_loss        | -84.3     |\n", "|    reward             | 2.3256698 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 4.58      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 7100      |\n", "|    time_elapsed       | 575       |\n", "|    total_timesteps    | 35500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.4     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 7099      |\n", "|    policy_loss        | -2.44     |\n", "|    reward             | 0.9263134 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 1.21      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 7200       |\n", "|    time_elapsed       | 586        |\n", "|    total_timesteps    | 36000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.5      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 7199       |\n", "|    policy_loss        | -228       |\n", "|    reward             | -1.9283981 |\n", "|    std                | 1.01       |\n", "|    value_loss         | 42         |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 7300      |\n", "|    time_elapsed       | 596       |\n", "|    total_timesteps    | 36500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.5     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 7299      |\n", "|    policy_loss        | 81        |\n", "|    reward             | -6.168546 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 8.16      |\n", "-------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 61          |\n", "|    iterations         | 7400        |\n", "|    time_elapsed       | 602         |\n", "|    total_timesteps    | 37000       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.5       |\n", "|    explained_variance | -1.19e-07   |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 7399        |\n", "|    policy_loss        | -136        |\n", "|    reward             | -0.66517484 |\n", "|    std                | 1.02        |\n", "|    value_loss         | 12.4        |\n", "---------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 7500       |\n", "|    time_elapsed       | 610        |\n", "|    total_timesteps    | 37500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.6      |\n", "|    explained_variance | 1.44e-05   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 7499       |\n", "|    policy_loss        | -368       |\n", "|    reward             | 0.28679553 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 81.7       |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 61          |\n", "|    iterations         | 7600        |\n", "|    time_elapsed       | 620         |\n", "|    total_timesteps    | 38000       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.6       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 7599        |\n", "|    policy_loss        | -80.4       |\n", "|    reward             | -0.02342434 |\n", "|    std                | 1.02        |\n", "|    value_loss         | 4.71        |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 7700      |\n", "|    time_elapsed       | 626       |\n", "|    total_timesteps    | 38500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.6     |\n", "|    explained_variance | -3.11     |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 7699      |\n", "|    policy_loss        | -91.7     |\n", "|    reward             | 2.6142132 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 6.61      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 7800       |\n", "|    time_elapsed       | 635        |\n", "|    total_timesteps    | 39000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.6      |\n", "|    explained_variance | 0.125      |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 7799       |\n", "|    policy_loss        | 4.76       |\n", "|    reward             | -1.2840562 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 0.762      |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 61          |\n", "|    iterations         | 7900        |\n", "|    time_elapsed       | 644         |\n", "|    total_timesteps    | 39500       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.7       |\n", "|    explained_variance | 0.0476      |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 7899        |\n", "|    policy_loss        | 273         |\n", "|    reward             | -0.55217224 |\n", "|    std                | 1.02        |\n", "|    value_loss         | 46.2        |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 8000      |\n", "|    time_elapsed       | 650       |\n", "|    total_timesteps    | 40000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.6     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 7999      |\n", "|    policy_loss        | -769      |\n", "|    reward             | 1.6622137 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 367       |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 8100       |\n", "|    time_elapsed       | 660        |\n", "|    total_timesteps    | 40500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.6      |\n", "|    explained_variance | -0.131     |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 8099       |\n", "|    policy_loss        | 38.2       |\n", "|    reward             | 0.38162667 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 1.09       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 8200      |\n", "|    time_elapsed       | 668       |\n", "|    total_timesteps    | 41000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.6     |\n", "|    explained_variance | -0.306    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 8199      |\n", "|    policy_loss        | 40.8      |\n", "|    reward             | 0.8386523 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 3.26      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 8300      |\n", "|    time_elapsed       | 674       |\n", "|    total_timesteps    | 41500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.5     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 8299      |\n", "|    policy_loss        | 41.7      |\n", "|    reward             | 1.5822707 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 4.75      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 8400      |\n", "|    time_elapsed       | 684       |\n", "|    total_timesteps    | 42000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.6     |\n", "|    explained_variance | 5.96e-08  |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 8399      |\n", "|    policy_loss        | 133       |\n", "|    reward             | 0.1792632 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 15.6      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 8500      |\n", "|    time_elapsed       | 691       |\n", "|    total_timesteps    | 42500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.7     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 8499      |\n", "|    policy_loss        | 99.2      |\n", "|    reward             | 1.6896911 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 33.2      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 8600      |\n", "|    time_elapsed       | 699       |\n", "|    total_timesteps    | 43000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.7     |\n", "|    explained_variance | -0.0163   |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 8599      |\n", "|    policy_loss        | -836      |\n", "|    reward             | 30.580954 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 436       |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 8700      |\n", "|    time_elapsed       | 709       |\n", "|    total_timesteps    | 43500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.7     |\n", "|    explained_variance | 0.0669    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 8699      |\n", "|    policy_loss        | -430      |\n", "|    reward             | -9.169519 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 186       |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 8800      |\n", "|    time_elapsed       | 715       |\n", "|    total_timesteps    | 44000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.7     |\n", "|    explained_variance | 0.178     |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 8799      |\n", "|    policy_loss        | -2.39     |\n", "|    reward             | -0.505542 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 0.0762    |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 8900      |\n", "|    time_elapsed       | 723       |\n", "|    total_timesteps    | 44500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.7     |\n", "|    explained_variance | 0.0519    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 8899      |\n", "|    policy_loss        | -29       |\n", "|    reward             | 1.4009765 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 0.617     |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 9000       |\n", "|    time_elapsed       | 733        |\n", "|    total_timesteps    | 45000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.8      |\n", "|    explained_variance | 0.0464     |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 8999       |\n", "|    policy_loss        | -142       |\n", "|    reward             | -1.6482956 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 12.1       |\n", "--------------------------------------\n", "----------------------------------------\n", "| time/                 |              |\n", "|    fps                | 61           |\n", "|    iterations         | 9100         |\n", "|    time_elapsed       | 739          |\n", "|    total_timesteps    | 45500        |\n", "| train/                |              |\n", "|    entropy_loss       | -41.7        |\n", "|    explained_variance | 0.128        |\n", "|    learning_rate      | 0.0007       |\n", "|    n_updates          | 9099         |\n", "|    policy_loss        | -18.8        |\n", "|    reward             | -0.022230674 |\n", "|    std                | 1.02         |\n", "|    value_loss         | 1.08         |\n", "----------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 61          |\n", "|    iterations         | 9200        |\n", "|    time_elapsed       | 748         |\n", "|    total_timesteps    | 46000       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.8       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 9199        |\n", "|    policy_loss        | 35.8        |\n", "|    reward             | -0.16132466 |\n", "|    std                | 1.02        |\n", "|    value_loss         | 6.5         |\n", "---------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 9300       |\n", "|    time_elapsed       | 757        |\n", "|    total_timesteps    | 46500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.7      |\n", "|    explained_variance | 0.00416    |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 9299       |\n", "|    policy_loss        | -192       |\n", "|    reward             | -3.3674068 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 87.7       |\n", "--------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 61       |\n", "|    iterations         | 9400     |\n", "|    time_elapsed       | 763      |\n", "|    total_timesteps    | 47000    |\n", "| train/                |          |\n", "|    entropy_loss       | -41.8    |\n", "|    explained_variance | -0.0393  |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 9399     |\n", "|    policy_loss        | -37.6    |\n", "|    reward             | 1.150722 |\n", "|    std                | 1.02     |\n", "|    value_loss         | 3.37     |\n", "------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 9500       |\n", "|    time_elapsed       | 772        |\n", "|    total_timesteps    | 47500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.8      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 9499       |\n", "|    policy_loss        | -25.2      |\n", "|    reward             | 0.41208658 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 0.698      |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 9600      |\n", "|    time_elapsed       | 780       |\n", "|    total_timesteps    | 48000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.9     |\n", "|    explained_variance | -1.19e-07 |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 9599      |\n", "|    policy_loss        | 9.9       |\n", "|    reward             | 0.5765088 |\n", "|    std                | 1.03      |\n", "|    value_loss         | 2.06      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 9700       |\n", "|    time_elapsed       | 787        |\n", "|    total_timesteps    | 48500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.9      |\n", "|    explained_variance | -1.19e-07  |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 9699       |\n", "|    policy_loss        | 315        |\n", "|    reward             | -0.2841707 |\n", "|    std                | 1.03       |\n", "|    value_loss         | 49.4       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 9800      |\n", "|    time_elapsed       | 797       |\n", "|    total_timesteps    | 49000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.9     |\n", "|    explained_variance | 5.96e-08  |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 9799      |\n", "|    policy_loss        | 125       |\n", "|    reward             | 0.6355639 |\n", "|    std                | 1.03      |\n", "|    value_loss         | 9.9       |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 61         |\n", "|    iterations         | 9900       |\n", "|    time_elapsed       | 804        |\n", "|    total_timesteps    | 49500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.9      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 9899       |\n", "|    policy_loss        | 155        |\n", "|    reward             | -4.6037025 |\n", "|    std                | 1.03       |\n", "|    value_loss         | 16.2       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 61        |\n", "|    iterations         | 10000     |\n", "|    time_elapsed       | 811       |\n", "|    total_timesteps    | 50000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.9     |\n", "|    explained_variance | -1.19e-07 |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 9999      |\n", "|    policy_loss        | 104       |\n", "|    reward             | -3.306132 |\n", "|    std                | 1.03      |\n", "|    value_loss         | 9.5       |\n", "-------------------------------------\n", "{'batch_size': 128, 'buffer_size': 50000, 'learning_rate': 0.001}\n", "Using cpu device\n", "Logging to results/ddpg\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 4          |\n", "|    fps             | 24         |\n", "|    time_elapsed    | 547        |\n", "|    total_timesteps | 13412      |\n", "| train/             |            |\n", "|    actor_loss      | 12.5       |\n", "|    critic_loss     | 295        |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 10059      |\n", "|    reward          | -6.3763723 |\n", "-----------------------------------\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 8          |\n", "|    fps             | 21         |\n", "|    time_elapsed    | 1237       |\n", "|    total_timesteps | 26824      |\n", "| train/             |            |\n", "|    actor_loss      | -5.51      |\n", "|    critic_loss     | 14.7       |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 23471      |\n", "|    reward          | -6.3763723 |\n", "-----------------------------------\n", "day: 3352, episode: 10\n", "begin_total_asset: 1011382.29\n", "end_total_asset: 6058882.63\n", "total_reward: 5047500.34\n", "total_cost: 1010.37\n", "total_trades: 46928\n", "Sharpe: 0.807\n", "=================================\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 12         |\n", "|    fps             | 20         |\n", "|    time_elapsed    | 1942       |\n", "|    total_timesteps | 40236      |\n", "| train/             |            |\n", "|    actor_loss      | -8.67      |\n", "|    critic_loss     | 7.57       |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 36883      |\n", "|    reward          | -6.3763723 |\n", "-----------------------------------\n", "{'n_steps': 2048, 'ent_coef': 0.01, 'learning_rate': 0.00025, 'batch_size': 128}\n", "Using cpu device\n", "Logging to results/ppo\n", "------------------------------------\n", "| time/              |             |\n", "|    fps             | 60          |\n", "|    iterations      | 1           |\n", "|    time_elapsed    | 33          |\n", "|    total_timesteps | 2048        |\n", "| train/             |             |\n", "|    reward          | -0.20400214 |\n", "------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 61          |\n", "|    iterations           | 2           |\n", "|    time_elapsed         | 66          |\n", "|    total_timesteps      | 4096        |\n", "| train/                  |             |\n", "|    approx_kl            | 0.019446293 |\n", "|    clip_fraction        | 0.218       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.2       |\n", "|    explained_variance   | -0.0153     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 8.32        |\n", "|    n_updates            | 10          |\n", "|    policy_gradient_loss | -0.0239     |\n", "|    reward               | 0.964798    |\n", "|    std                  | 1           |\n", "|    value_loss           | 12.1        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 60          |\n", "|    iterations           | 3           |\n", "|    time_elapsed         | 100         |\n", "|    total_timesteps      | 6144        |\n", "| train/                  |             |\n", "|    approx_kl            | 0.017402954 |\n", "|    clip_fraction        | 0.182       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.2       |\n", "|    explained_variance   | 0.000782    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 28.3        |\n", "|    n_updates            | 20          |\n", "|    policy_gradient_loss | -0.0164     |\n", "|    reward               | 7.5384216   |\n", "|    std                  | 1           |\n", "|    value_loss           | 51.3        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 4           |\n", "|    time_elapsed         | 131         |\n", "|    total_timesteps      | 8192        |\n", "| train/                  |             |\n", "|    approx_kl            | 0.015737543 |\n", "|    clip_fraction        | 0.162       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.3       |\n", "|    explained_variance   | -0.0037     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 22.7        |\n", "|    n_updates            | 30          |\n", "|    policy_gradient_loss | -0.0225     |\n", "|    reward               | 2.27421     |\n", "|    std                  | 1.01        |\n", "|    value_loss           | 38.2        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 5           |\n", "|    time_elapsed         | 165         |\n", "|    total_timesteps      | 10240       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.020310912 |\n", "|    clip_fraction        | 0.184       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.3       |\n", "|    explained_variance   | -0.00721    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 12          |\n", "|    n_updates            | 40          |\n", "|    policy_gradient_loss | -0.0202     |\n", "|    reward               | 0.7753585   |\n", "|    std                  | 1.01        |\n", "|    value_loss           | 21.3        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 6           |\n", "|    time_elapsed         | 196         |\n", "|    total_timesteps      | 12288       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.014960194 |\n", "|    clip_fraction        | 0.143       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.4       |\n", "|    explained_variance   | -0.0299     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 30.5        |\n", "|    n_updates            | 50          |\n", "|    policy_gradient_loss | -0.0179     |\n", "|    reward               | 2.62347     |\n", "|    std                  | 1.01        |\n", "|    value_loss           | 46.2        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 7           |\n", "|    time_elapsed         | 228         |\n", "|    total_timesteps      | 14336       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.023127541 |\n", "|    clip_fraction        | 0.193       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.4       |\n", "|    explained_variance   | -0.023      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 6.36        |\n", "|    n_updates            | 60          |\n", "|    policy_gradient_loss | -0.0221     |\n", "|    reward               | 1.0379714   |\n", "|    std                  | 1.01        |\n", "|    value_loss           | 14.1        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 8           |\n", "|    time_elapsed         | 260         |\n", "|    total_timesteps      | 16384       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.018745095 |\n", "|    clip_fraction        | 0.201       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.5       |\n", "|    explained_variance   | 0.00254     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 18.3        |\n", "|    n_updates            | 70          |\n", "|    policy_gradient_loss | -0.019      |\n", "|    reward               | -0.21705139 |\n", "|    std                  | 1.01        |\n", "|    value_loss           | 59.5        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 9           |\n", "|    time_elapsed         | 294         |\n", "|    total_timesteps      | 18432       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.018167643 |\n", "|    clip_fraction        | 0.154       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.5       |\n", "|    explained_variance   | -0.000664   |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 21.5        |\n", "|    n_updates            | 80          |\n", "|    policy_gradient_loss | -0.0144     |\n", "|    reward               | -0.31962025 |\n", "|    std                  | 1.01        |\n", "|    value_loss           | 36.6        |\n", "-----------------------------------------\n", "----------------------------------------\n", "| time/                   |            |\n", "|    fps                  | 62         |\n", "|    iterations           | 10         |\n", "|    time_elapsed         | 328        |\n", "|    total_timesteps      | 20480      |\n", "| train/                  |            |\n", "|    approx_kl            | 0.02108417 |\n", "|    clip_fraction        | 0.244      |\n", "|    clip_range           | 0.2        |\n", "|    entropy_loss         | -41.6      |\n", "|    explained_variance   | 0.0203     |\n", "|    learning_rate        | 0.00025    |\n", "|    loss                 | 7.36       |\n", "|    n_updates            | 90         |\n", "|    policy_gradient_loss | -0.0191    |\n", "|    reward               | 0.07936729 |\n", "|    std                  | 1.02       |\n", "|    value_loss           | 23.3       |\n", "----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 11          |\n", "|    time_elapsed         | 357         |\n", "|    total_timesteps      | 22528       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.014700897 |\n", "|    clip_fraction        | 0.166       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.6       |\n", "|    explained_variance   | 0.00383     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 29.1        |\n", "|    n_updates            | 100         |\n", "|    policy_gradient_loss | -0.0156     |\n", "|    reward               | 1.4870173   |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 93.3        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 12          |\n", "|    time_elapsed         | 391         |\n", "|    total_timesteps      | 24576       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.017688308 |\n", "|    clip_fraction        | 0.194       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.7       |\n", "|    explained_variance   | -0.0104     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 6.58        |\n", "|    n_updates            | 110         |\n", "|    policy_gradient_loss | -0.0161     |\n", "|    reward               | -0.7623598  |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 17.5        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 13          |\n", "|    time_elapsed         | 422         |\n", "|    total_timesteps      | 26624       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.023069832 |\n", "|    clip_fraction        | 0.24        |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.7       |\n", "|    explained_variance   | 0.0101      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 38.8        |\n", "|    n_updates            | 120         |\n", "|    policy_gradient_loss | -0.0147     |\n", "|    reward               | 3.4454083   |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 64.9        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 14          |\n", "|    time_elapsed         | 454         |\n", "|    total_timesteps      | 28672       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.017561657 |\n", "|    clip_fraction        | 0.204       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.7       |\n", "|    explained_variance   | -0.0172     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 21.4        |\n", "|    n_updates            | 130         |\n", "|    policy_gradient_loss | -0.02       |\n", "|    reward               | 0.9586051   |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 52.4        |\n", "-----------------------------------------\n", "day: 3352, episode: 10\n", "begin_total_asset: 988584.72\n", "end_total_asset: 3416710.65\n", "total_reward: 2428125.94\n", "total_cost: 420148.99\n", "total_trades: 89136\n", "Sharpe: 0.598\n", "=================================\n", "----------------------------------------\n", "| time/                   |            |\n", "|    fps                  | 63         |\n", "|    iterations           | 15         |\n", "|    time_elapsed         | 487        |\n", "|    total_timesteps      | 30720      |\n", "| train/                  |            |\n", "|    approx_kl            | 0.02006042 |\n", "|    clip_fraction        | 0.219      |\n", "|    clip_range           | 0.2        |\n", "|    entropy_loss         | -41.7      |\n", "|    explained_variance   | -0.0279    |\n", "|    learning_rate        | 0.00025    |\n", "|    loss                 | 13.3       |\n", "|    n_updates            | 140        |\n", "|    policy_gradient_loss | -0.0185    |\n", "|    reward               | -0.3580386 |\n", "|    std                  | 1.02       |\n", "|    value_loss           | 23.2       |\n", "----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 16          |\n", "|    time_elapsed         | 523         |\n", "|    total_timesteps      | 32768       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.025233287 |\n", "|    clip_fraction        | 0.243       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.8       |\n", "|    explained_variance   | -0.00552    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 22.4        |\n", "|    n_updates            | 150         |\n", "|    policy_gradient_loss | -0.0176     |\n", "|    reward               | -0.5090524  |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 69.5        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 17          |\n", "|    time_elapsed         | 556         |\n", "|    total_timesteps      | 34816       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.022021335 |\n", "|    clip_fraction        | 0.216       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.8       |\n", "|    explained_variance   | 0.0188      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 8.75        |\n", "|    n_updates            | 160         |\n", "|    policy_gradient_loss | -0.0188     |\n", "|    reward               | 1.8985721   |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 23.2        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 18          |\n", "|    time_elapsed         | 586         |\n", "|    total_timesteps      | 36864       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.019396901 |\n", "|    clip_fraction        | 0.229       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.9       |\n", "|    explained_variance   | 0.00194     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 14.6        |\n", "|    n_updates            | 170         |\n", "|    policy_gradient_loss | -0.0195     |\n", "|    reward               | -0.31956208 |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 39.6        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 19          |\n", "|    time_elapsed         | 622         |\n", "|    total_timesteps      | 38912       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.020318478 |\n", "|    clip_fraction        | 0.225       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.9       |\n", "|    explained_variance   | 0.0132      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 22.3        |\n", "|    n_updates            | 180         |\n", "|    policy_gradient_loss | -0.0128     |\n", "|    reward               | 0.33881456  |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 55.7        |\n", "-----------------------------------------\n", "----------------------------------------\n", "| time/                   |            |\n", "|    fps                  | 62         |\n", "|    iterations           | 20         |\n", "|    time_elapsed         | 652        |\n", "|    total_timesteps      | 40960      |\n", "| train/                  |            |\n", "|    approx_kl            | 0.02080874 |\n", "|    clip_fraction        | 0.179      |\n", "|    clip_range           | 0.2        |\n", "|    entropy_loss         | -42        |\n", "|    explained_variance   | 0.0334     |\n", "|    learning_rate        | 0.00025    |\n", "|    loss                 | 5.55       |\n", "|    n_updates            | 190        |\n", "|    policy_gradient_loss | -0.0186    |\n", "|    reward               | 0.15585361 |\n", "|    std                  | 1.03       |\n", "|    value_loss           | 19.1       |\n", "----------------------------------------\n", "----------------------------------------\n", "| time/                   |            |\n", "|    fps                  | 62         |\n", "|    iterations           | 21         |\n", "|    time_elapsed         | 686        |\n", "|    total_timesteps      | 43008      |\n", "| train/                  |            |\n", "|    approx_kl            | 0.01973752 |\n", "|    clip_fraction        | 0.227      |\n", "|    clip_range           | 0.2        |\n", "|    entropy_loss         | -42        |\n", "|    explained_variance   | 0.00997    |\n", "|    learning_rate        | 0.00025    |\n", "|    loss                 | 19         |\n", "|    n_updates            | 200        |\n", "|    policy_gradient_loss | -0.0153    |\n", "|    reward               | -14.07267  |\n", "|    std                  | 1.03       |\n", "|    value_loss           | 75.2       |\n", "----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 22          |\n", "|    time_elapsed         | 717         |\n", "|    total_timesteps      | 45056       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.013898542 |\n", "|    clip_fraction        | 0.0931      |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42         |\n", "|    explained_variance   | -0.000876   |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 12.2        |\n", "|    n_updates            | 210         |\n", "|    policy_gradient_loss | -0.0138     |\n", "|    reward               | -5.085373   |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 27          |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 23          |\n", "|    time_elapsed         | 750         |\n", "|    total_timesteps      | 47104       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.01667095  |\n", "|    clip_fraction        | 0.185       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.1       |\n", "|    explained_variance   | 0.00379     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 8.83        |\n", "|    n_updates            | 220         |\n", "|    policy_gradient_loss | -0.0139     |\n", "|    reward               | -0.11939671 |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 30.8        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 24          |\n", "|    time_elapsed         | 785         |\n", "|    total_timesteps      | 49152       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.027711859 |\n", "|    clip_fraction        | 0.253       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.1       |\n", "|    explained_variance   | 0.0238      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 31.9        |\n", "|    n_updates            | 230         |\n", "|    policy_gradient_loss | -0.00308    |\n", "|    reward               | -1.080327   |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 75          |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 62          |\n", "|    iterations           | 25          |\n", "|    time_elapsed         | 817         |\n", "|    total_timesteps      | 51200       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.025901645 |\n", "|    clip_fraction        | 0.278       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.1       |\n", "|    explained_variance   | 0.0481      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 5.34        |\n", "|    n_updates            | 240         |\n", "|    policy_gradient_loss | -0.0164     |\n", "|    reward               | 0.08477563  |\n", "|    std                  | 1.04        |\n", "|    value_loss           | 13          |\n", "-----------------------------------------\n", "{'batch_size': 128, 'buffer_size': 100000, 'learning_rate': 0.0001, 'learning_starts': 100, 'ent_coef': 'auto_0.1'}\n", "Using cpu device\n", "Logging to results/sac\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 4          |\n", "|    fps             | 19         |\n", "|    time_elapsed    | 703        |\n", "|    total_timesteps | 13412      |\n", "| train/             |            |\n", "|    actor_loss      | 1.68e+03   |\n", "|    critic_loss     | 1e+04      |\n", "|    ent_coef        | 0.309      |\n", "|    ent_coef_loss   | -0.516     |\n", "|    learning_rate   | 0.0001     |\n", "|    n_updates       | 13311      |\n", "|    reward          | -11.183781 |\n", "-----------------------------------\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 8          |\n", "|    fps             | 19         |\n", "|    time_elapsed    | 1410       |\n", "|    total_timesteps | 26824      |\n", "| train/             |            |\n", "|    actor_loss      | 677        |\n", "|    critic_loss     | 66.2       |\n", "|    ent_coef        | 0.0855     |\n", "|    ent_coef_loss   | -112       |\n", "|    learning_rate   | 0.0001     |\n", "|    n_updates       | 26723      |\n", "|    reward          | -10.753805 |\n", "-----------------------------------\n", "day: 3352, episode: 10\n", "begin_total_asset: 1005927.23\n", "end_total_asset: 5294689.46\n", "total_reward: 4288762.22\n", "total_cost: 37988.65\n", "total_trades: 61507\n", "Sharpe: 0.700\n", "=================================\n", "----------------------------------\n", "| time/              |           |\n", "|    episodes        | 12        |\n", "|    fps             | 18        |\n", "|    time_elapsed    | 2126      |\n", "|    total_timesteps | 40236     |\n", "| train/             |           |\n", "|    actor_loss      | 304       |\n", "|    critic_loss     | 20.1      |\n", "|    ent_coef        | 0.0227    |\n", "|    ent_coef_loss   | -145      |\n", "|    learning_rate   | 0.0001    |\n", "|    n_updates       | 40135     |\n", "|    reward          | -9.593834 |\n", "----------------------------------\n", "{'batch_size': 100, 'buffer_size': 1000000, 'learning_rate': 0.001}\n", "Using cpu device\n", "Logging to results/td3\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 4          |\n", "|    fps             | 24         |\n", "|    time_elapsed    | 544        |\n", "|    total_timesteps | 13412      |\n", "| train/             |            |\n", "|    actor_loss      | 132        |\n", "|    critic_loss     | 6.19e+03   |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 10059      |\n", "|    reward          | -2.3487854 |\n", "-----------------------------------\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 8          |\n", "|    fps             | 21         |\n", "|    time_elapsed    | 1242       |\n", "|    total_timesteps | 26824      |\n", "| train/             |            |\n", "|    actor_loss      | 49         |\n", "|    critic_loss     | 584        |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 23471      |\n", "|    reward          | -2.3487854 |\n", "-----------------------------------\n", "day: 3352, episode: 10\n", "begin_total_asset: 1012427.98\n", "end_total_asset: 5866237.13\n", "total_reward: 4853809.15\n", "total_cost: 1011.41\n", "total_trades: 53632\n", "Sharpe: 0.831\n", "=================================\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 12         |\n", "|    fps             | 20         |\n", "|    time_elapsed    | 1943       |\n", "|    total_timesteps | 40236      |\n", "| train/             |            |\n", "|    actor_loss      | 37.3       |\n", "|    critic_loss     | 101        |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 36883      |\n", "|    reward          | -2.3487854 |\n", "-----------------------------------\n", "hit end!\n", "hit end!\n", "hit end!\n", "hit end!\n", "hit end!\n", "[*********************100%***********************]  1 of 1 completed\n", "Shape of DataFrame:  (22, 8)\n", "i:  3\n", "{'n_steps': 5, 'ent_coef': 0.01, 'learning_rate': 0.0007}\n", "Using cpu device\n", "Logging to results/a2c\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 46        |\n", "|    iterations         | 100       |\n", "|    time_elapsed       | 10        |\n", "|    total_timesteps    | 500       |\n", "| train/                |           |\n", "|    entropy_loss       | -41.2     |\n", "|    explained_variance | -0.471    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 99        |\n", "|    policy_loss        | 86.2      |\n", "|    reward             | 1.3343517 |\n", "|    std                | 1         |\n", "|    value_loss         | 5.99      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 47         |\n", "|    iterations         | 200        |\n", "|    time_elapsed       | 20         |\n", "|    total_timesteps    | 1000       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.3      |\n", "|    explained_variance | -0.271     |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 199        |\n", "|    policy_loss        | 44.4       |\n", "|    reward             | -1.4969016 |\n", "|    std                | 1          |\n", "|    value_loss         | 1.22       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 47         |\n", "|    iterations         | 300        |\n", "|    time_elapsed       | 31         |\n", "|    total_timesteps    | 1500       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.3      |\n", "|    explained_variance | 0.0667     |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 299        |\n", "|    policy_loss        | 141        |\n", "|    reward             | -4.3429856 |\n", "|    std                | 1          |\n", "|    value_loss         | 15.6       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 51         |\n", "|    iterations         | 400        |\n", "|    time_elapsed       | 39         |\n", "|    total_timesteps    | 2000       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.3      |\n", "|    explained_variance | -1.19e-07  |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 399        |\n", "|    policy_loss        | 28.9       |\n", "|    reward             | -2.9280229 |\n", "|    std                | 1.01       |\n", "|    value_loss         | 0.941      |\n", "--------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 54       |\n", "|    iterations         | 500      |\n", "|    time_elapsed       | 46       |\n", "|    total_timesteps    | 2500     |\n", "| train/                |          |\n", "|    entropy_loss       | -41.2    |\n", "|    explained_variance | 0        |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 499      |\n", "|    policy_loss        | -356     |\n", "|    reward             | 2.440834 |\n", "|    std                | 1        |\n", "|    value_loss         | 95.7     |\n", "------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 52        |\n", "|    iterations         | 600       |\n", "|    time_elapsed       | 56        |\n", "|    total_timesteps    | 3000      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.3     |\n", "|    explained_variance | -1.19e-07 |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 599       |\n", "|    policy_loss        | -187      |\n", "|    reward             | 7.7011724 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 30.6      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 55         |\n", "|    iterations         | 700        |\n", "|    time_elapsed       | 63         |\n", "|    total_timesteps    | 3500       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.4      |\n", "|    explained_variance | -0.042     |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 699        |\n", "|    policy_loss        | 23.3       |\n", "|    reward             | -1.0782235 |\n", "|    std                | 1.01       |\n", "|    value_loss         | 0.963      |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 56          |\n", "|    iterations         | 800         |\n", "|    time_elapsed       | 71          |\n", "|    total_timesteps    | 4000        |\n", "| train/                |             |\n", "|    entropy_loss       | -41.4       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 799         |\n", "|    policy_loss        | -258        |\n", "|    reward             | -0.20911986 |\n", "|    std                | 1.01        |\n", "|    value_loss         | 50.4        |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 55        |\n", "|    iterations         | 900       |\n", "|    time_elapsed       | 81        |\n", "|    total_timesteps    | 4500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.4     |\n", "|    explained_variance | 0.118     |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 899       |\n", "|    policy_loss        | -66.9     |\n", "|    reward             | 0.8433642 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 2.9       |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 57         |\n", "|    iterations         | 1000       |\n", "|    time_elapsed       | 87         |\n", "|    total_timesteps    | 5000       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.4      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 999        |\n", "|    policy_loss        | 5.19       |\n", "|    reward             | -1.4874439 |\n", "|    std                | 1.01       |\n", "|    value_loss         | 4.01       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 57        |\n", "|    iterations         | 1100      |\n", "|    time_elapsed       | 96        |\n", "|    total_timesteps    | 5500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.4     |\n", "|    explained_variance | -0.555    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 1099      |\n", "|    policy_loss        | -77.7     |\n", "|    reward             | 1.8939301 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 3.97      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 57        |\n", "|    iterations         | 1200      |\n", "|    time_elapsed       | 105       |\n", "|    total_timesteps    | 6000      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.4     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 1199      |\n", "|    policy_loss        | 187       |\n", "|    reward             | 2.3026025 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 33.4      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 58        |\n", "|    iterations         | 1300      |\n", "|    time_elapsed       | 111       |\n", "|    total_timesteps    | 6500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.4     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 1299      |\n", "|    policy_loss        | 47.1      |\n", "|    reward             | 1.9173757 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 5.89      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 54         |\n", "|    iterations         | 1400       |\n", "|    time_elapsed       | 127        |\n", "|    total_timesteps    | 7000       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.4      |\n", "|    explained_variance | -0.0644    |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 1399       |\n", "|    policy_loss        | 32.9       |\n", "|    reward             | -3.0739012 |\n", "|    std                | 1.01       |\n", "|    value_loss         | 1.06       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 55        |\n", "|    iterations         | 1500      |\n", "|    time_elapsed       | 134       |\n", "|    total_timesteps    | 7500      |\n", "| train/                |           |\n", "|    entropy_loss       | -41.4     |\n", "|    explained_variance | -1.19e-07 |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 1499      |\n", "|    policy_loss        | 305       |\n", "|    reward             | 2.5744946 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 50.5      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 55         |\n", "|    iterations         | 1600       |\n", "|    time_elapsed       | 144        |\n", "|    total_timesteps    | 8000       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.4      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 1599       |\n", "|    policy_loss        | -4.88      |\n", "|    reward             | -2.1707737 |\n", "|    std                | 1.01       |\n", "|    value_loss         | 1.28       |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 55          |\n", "|    iterations         | 1700        |\n", "|    time_elapsed       | 151         |\n", "|    total_timesteps    | 8500        |\n", "| train/                |             |\n", "|    entropy_loss       | -41.5       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 1699        |\n", "|    policy_loss        | 160         |\n", "|    reward             | -0.81266195 |\n", "|    std                | 1.01        |\n", "|    value_loss         | 16          |\n", "---------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 56         |\n", "|    iterations         | 1800       |\n", "|    time_elapsed       | 158        |\n", "|    total_timesteps    | 9000       |\n", "| train/                |            |\n", "|    entropy_loss       | -41.5      |\n", "|    explained_variance | -0.208     |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 1799       |\n", "|    policy_loss        | 38.1       |\n", "|    reward             | -1.4904355 |\n", "|    std                | 1.01       |\n", "|    value_loss         | 5.15       |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 56          |\n", "|    iterations         | 1900        |\n", "|    time_elapsed       | 169         |\n", "|    total_timesteps    | 9500        |\n", "| train/                |             |\n", "|    entropy_loss       | -41.5       |\n", "|    explained_variance | 1.79e-07    |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 1899        |\n", "|    policy_loss        | 282         |\n", "|    reward             | -0.36043915 |\n", "|    std                | 1.01        |\n", "|    value_loss         | 56          |\n", "---------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 56          |\n", "|    iterations         | 2000        |\n", "|    time_elapsed       | 175         |\n", "|    total_timesteps    | 10000       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.5       |\n", "|    explained_variance | -0.0747     |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 1999        |\n", "|    policy_loss        | -471        |\n", "|    reward             | -0.37017918 |\n", "|    std                | 1.01        |\n", "|    value_loss         | 171         |\n", "---------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 57         |\n", "|    iterations         | 2100       |\n", "|    time_elapsed       | 183        |\n", "|    total_timesteps    | 10500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.5      |\n", "|    explained_variance | 0.163      |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 2099       |\n", "|    policy_loss        | 1.74       |\n", "|    reward             | -1.2063048 |\n", "|    std                | 1.01       |\n", "|    value_loss         | 0.28       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 56        |\n", "|    iterations         | 2200      |\n", "|    time_elapsed       | 193       |\n", "|    total_timesteps    | 11000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.6     |\n", "|    explained_variance | -0.326    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 2199      |\n", "|    policy_loss        | -94       |\n", "|    reward             | 1.8247845 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 5.61      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 57        |\n", "|    iterations         | 2300      |\n", "|    time_elapsed       | 199       |\n", "|    total_timesteps    | 11500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.6     |\n", "|    explained_variance | 0.0682    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 2299      |\n", "|    policy_loss        | -128      |\n", "|    reward             | 0.4869665 |\n", "|    std                | 1.01      |\n", "|    value_loss         | 15.7      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 57         |\n", "|    iterations         | 2400       |\n", "|    time_elapsed       | 208        |\n", "|    total_timesteps    | 12000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.5      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 2399       |\n", "|    policy_loss        | -173       |\n", "|    reward             | -0.9164407 |\n", "|    std                | 1.01       |\n", "|    value_loss         | 23.1       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 57        |\n", "|    iterations         | 2500      |\n", "|    time_elapsed       | 217       |\n", "|    total_timesteps    | 12500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.6     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 2499      |\n", "|    policy_loss        | 86.3      |\n", "|    reward             | 3.2540042 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 5.33      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 58        |\n", "|    iterations         | 2600      |\n", "|    time_elapsed       | 223       |\n", "|    total_timesteps    | 13000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.6     |\n", "|    explained_variance | 1.19e-07  |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 2599      |\n", "|    policy_loss        | 428       |\n", "|    reward             | 2.4169402 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 112       |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 57         |\n", "|    iterations         | 2700       |\n", "|    time_elapsed       | 233        |\n", "|    total_timesteps    | 13500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.6      |\n", "|    explained_variance | 0.0601     |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 2699       |\n", "|    policy_loss        | 1.73       |\n", "|    reward             | -1.3785244 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 0.378      |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 58         |\n", "|    iterations         | 2800       |\n", "|    time_elapsed       | 241        |\n", "|    total_timesteps    | 14000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.7      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 2799       |\n", "|    policy_loss        | 45.3       |\n", "|    reward             | -1.8347946 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 4.25       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 58         |\n", "|    iterations         | 2900       |\n", "|    time_elapsed       | 247        |\n", "|    total_timesteps    | 14500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.7      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 2899       |\n", "|    policy_loss        | -49.6      |\n", "|    reward             | 0.13086061 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 1.75       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 58         |\n", "|    iterations         | 3000       |\n", "|    time_elapsed       | 258        |\n", "|    total_timesteps    | 15000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.7      |\n", "|    explained_variance | -0.104     |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 2999       |\n", "|    policy_loss        | -51.1      |\n", "|    reward             | -2.9340496 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 1.92       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 58        |\n", "|    iterations         | 3100      |\n", "|    time_elapsed       | 266       |\n", "|    total_timesteps    | 15500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.7     |\n", "|    explained_variance | 5.96e-08  |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 3099      |\n", "|    policy_loss        | -96       |\n", "|    reward             | 5.6104155 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 7.44      |\n", "-------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 58       |\n", "|    iterations         | 3200     |\n", "|    time_elapsed       | 273      |\n", "|    total_timesteps    | 16000    |\n", "| train/                |          |\n", "|    entropy_loss       | -41.7    |\n", "|    explained_variance | 0        |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 3199     |\n", "|    policy_loss        | 288      |\n", "|    reward             | 4.10712  |\n", "|    std                | 1.02     |\n", "|    value_loss         | 56       |\n", "------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 58         |\n", "|    iterations         | 3300       |\n", "|    time_elapsed       | 283        |\n", "|    total_timesteps    | 16500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.7      |\n", "|    explained_variance | 5.96e-08   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 3299       |\n", "|    policy_loss        | 29.9       |\n", "|    reward             | 0.10846165 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 6.75       |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 58          |\n", "|    iterations         | 3400        |\n", "|    time_elapsed       | 290         |\n", "|    total_timesteps    | 17000       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.7       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 3399        |\n", "|    policy_loss        | -128        |\n", "|    reward             | -0.26822066 |\n", "|    std                | 1.02        |\n", "|    value_loss         | 12.3        |\n", "---------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 58          |\n", "|    iterations         | 3500        |\n", "|    time_elapsed       | 298         |\n", "|    total_timesteps    | 17500       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.7       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 3499        |\n", "|    policy_loss        | 23.3        |\n", "|    reward             | 0.012110213 |\n", "|    std                | 1.02        |\n", "|    value_loss         | 0.832       |\n", "---------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 58         |\n", "|    iterations         | 3600       |\n", "|    time_elapsed       | 308        |\n", "|    total_timesteps    | 18000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.8      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 3599       |\n", "|    policy_loss        | -94.5      |\n", "|    reward             | -0.6443226 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 11.1       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 58        |\n", "|    iterations         | 3700      |\n", "|    time_elapsed       | 314       |\n", "|    total_timesteps    | 18500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.8     |\n", "|    explained_variance | 1.19e-07  |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 3699      |\n", "|    policy_loss        | -16.7     |\n", "|    reward             | 1.8698422 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 0.374     |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 58         |\n", "|    iterations         | 3800       |\n", "|    time_elapsed       | 323        |\n", "|    total_timesteps    | 19000      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.8      |\n", "|    explained_variance | 1.19e-07   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 3799       |\n", "|    policy_loss        | 166        |\n", "|    reward             | -1.3664656 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 19.5       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 58         |\n", "|    iterations         | 3900       |\n", "|    time_elapsed       | 332        |\n", "|    total_timesteps    | 19500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.7      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 3899       |\n", "|    policy_loss        | 43.9       |\n", "|    reward             | -1.1592114 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 2.46       |\n", "--------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 58       |\n", "|    iterations         | 4000     |\n", "|    time_elapsed       | 339      |\n", "|    total_timesteps    | 20000    |\n", "| train/                |          |\n", "|    entropy_loss       | -41.8    |\n", "|    explained_variance | 0        |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 3999     |\n", "|    policy_loss        | -31.6    |\n", "|    reward             | 1.018338 |\n", "|    std                | 1.02     |\n", "|    value_loss         | 0.683    |\n", "------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 58         |\n", "|    iterations         | 4100       |\n", "|    time_elapsed       | 348        |\n", "|    total_timesteps    | 20500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.8      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 4099       |\n", "|    policy_loss        | -21.4      |\n", "|    reward             | 0.26098472 |\n", "|    std                | 1.02       |\n", "|    value_loss         | 0.295      |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 58        |\n", "|    iterations         | 4200      |\n", "|    time_elapsed       | 356       |\n", "|    total_timesteps    | 21000     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.8     |\n", "|    explained_variance | 5.96e-08  |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4199      |\n", "|    policy_loss        | 37.3      |\n", "|    reward             | 2.0496662 |\n", "|    std                | 1.02      |\n", "|    value_loss         | 1.24      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 4300      |\n", "|    time_elapsed       | 362       |\n", "|    total_timesteps    | 21500     |\n", "| train/                |           |\n", "|    entropy_loss       | -41.9     |\n", "|    explained_variance | 5.96e-08  |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4299      |\n", "|    policy_loss        | 21.7      |\n", "|    reward             | 0.5919729 |\n", "|    std                | 1.03      |\n", "|    value_loss         | 0.614     |\n", "-------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 58          |\n", "|    iterations         | 4400        |\n", "|    time_elapsed       | 373         |\n", "|    total_timesteps    | 22000       |\n", "| train/                |             |\n", "|    entropy_loss       | -41.9       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 4399        |\n", "|    policy_loss        | -59.5       |\n", "|    reward             | -0.44648832 |\n", "|    std                | 1.03        |\n", "|    value_loss         | 2.35        |\n", "---------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 4500       |\n", "|    time_elapsed       | 380        |\n", "|    total_timesteps    | 22500      |\n", "| train/                |            |\n", "|    entropy_loss       | -41.9      |\n", "|    explained_variance | -1.19e-07  |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 4499       |\n", "|    policy_loss        | 75.7       |\n", "|    reward             | -1.7295737 |\n", "|    std                | 1.03       |\n", "|    value_loss         | 5.7        |\n", "--------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 59       |\n", "|    iterations         | 4600     |\n", "|    time_elapsed       | 387      |\n", "|    total_timesteps    | 23000    |\n", "| train/                |          |\n", "|    entropy_loss       | -41.9    |\n", "|    explained_variance | 0        |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 4599     |\n", "|    policy_loss        | -194     |\n", "|    reward             | -2.21535 |\n", "|    std                | 1.03     |\n", "|    value_loss         | 37.3     |\n", "------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 58         |\n", "|    iterations         | 4700       |\n", "|    time_elapsed       | 398        |\n", "|    total_timesteps    | 23500      |\n", "| train/                |            |\n", "|    entropy_loss       | -42        |\n", "|    explained_variance | -0.0141    |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 4699       |\n", "|    policy_loss        | -32.7      |\n", "|    reward             | 0.16243774 |\n", "|    std                | 1.03       |\n", "|    value_loss         | 1.75       |\n", "--------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 59       |\n", "|    iterations         | 4800     |\n", "|    time_elapsed       | 404      |\n", "|    total_timesteps    | 24000    |\n", "| train/                |          |\n", "|    entropy_loss       | -42      |\n", "|    explained_variance | 0.168    |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 4799     |\n", "|    policy_loss        | -61.7    |\n", "|    reward             | 0.961177 |\n", "|    std                | 1.03     |\n", "|    value_loss         | 2.67     |\n", "------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 59       |\n", "|    iterations         | 4900     |\n", "|    time_elapsed       | 412      |\n", "|    total_timesteps    | 24500    |\n", "| train/                |          |\n", "|    entropy_loss       | -42      |\n", "|    explained_variance | 0        |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 4899     |\n", "|    policy_loss        | -54.1    |\n", "|    reward             | 3.000443 |\n", "|    std                | 1.03     |\n", "|    value_loss         | 2.52     |\n", "------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 5000      |\n", "|    time_elapsed       | 422       |\n", "|    total_timesteps    | 25000     |\n", "| train/                |           |\n", "|    entropy_loss       | -42       |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 4999      |\n", "|    policy_loss        | 75.7      |\n", "|    reward             | 0.7883484 |\n", "|    std                | 1.03      |\n", "|    value_loss         | 6.61      |\n", "-------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 59          |\n", "|    iterations         | 5100        |\n", "|    time_elapsed       | 428         |\n", "|    total_timesteps    | 25500       |\n", "| train/                |             |\n", "|    entropy_loss       | -42.1       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 5099        |\n", "|    policy_loss        | 237         |\n", "|    reward             | -0.49083808 |\n", "|    std                | 1.03        |\n", "|    value_loss         | 39.1        |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 5200      |\n", "|    time_elapsed       | 437       |\n", "|    total_timesteps    | 26000     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.1     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 5199      |\n", "|    policy_loss        | 152       |\n", "|    reward             | 2.7196112 |\n", "|    std                | 1.03      |\n", "|    value_loss         | 16.1      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 5300       |\n", "|    time_elapsed       | 445        |\n", "|    total_timesteps    | 26500      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.1      |\n", "|    explained_variance | -1.19e-07  |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 5299       |\n", "|    policy_loss        | -317       |\n", "|    reward             | 0.59174556 |\n", "|    std                | 1.03       |\n", "|    value_loss         | 63.7       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 5400       |\n", "|    time_elapsed       | 452        |\n", "|    total_timesteps    | 27000      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.1      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 5399       |\n", "|    policy_loss        | -126       |\n", "|    reward             | 0.06384493 |\n", "|    std                | 1.03       |\n", "|    value_loss         | 9.43       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 5500       |\n", "|    time_elapsed       | 461        |\n", "|    total_timesteps    | 27500      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.1      |\n", "|    explained_variance | 1.19e-07   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 5499       |\n", "|    policy_loss        | -11.3      |\n", "|    reward             | -1.1629822 |\n", "|    std                | 1.03       |\n", "|    value_loss         | 0.213      |\n", "--------------------------------------\n", "------------------------------------\n", "| time/                 |          |\n", "|    fps                | 59       |\n", "|    iterations         | 5600     |\n", "|    time_elapsed       | 469      |\n", "|    total_timesteps    | 28000    |\n", "| train/                |          |\n", "|    entropy_loss       | -42.1    |\n", "|    explained_variance | 0        |\n", "|    learning_rate      | 0.0007   |\n", "|    n_updates          | 5599     |\n", "|    policy_loss        | 91       |\n", "|    reward             | 1.35537  |\n", "|    std                | 1.03     |\n", "|    value_loss         | 5.83     |\n", "------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 5700      |\n", "|    time_elapsed       | 476       |\n", "|    total_timesteps    | 28500     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.1     |\n", "|    explained_variance | 5.96e-08  |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 5699      |\n", "|    policy_loss        | -18.6     |\n", "|    reward             | -2.177703 |\n", "|    std                | 1.03      |\n", "|    value_loss         | 0.358     |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 5800       |\n", "|    time_elapsed       | 487        |\n", "|    total_timesteps    | 29000      |\n", "| train/                |            |\n", "|    entropy_loss       | -42        |\n", "|    explained_variance | -1.19e-07  |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 5799       |\n", "|    policy_loss        | -36.6      |\n", "|    reward             | -2.1937134 |\n", "|    std                | 1.03       |\n", "|    value_loss         | 2.54       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 5900       |\n", "|    time_elapsed       | 497        |\n", "|    total_timesteps    | 29500      |\n", "| train/                |            |\n", "|    entropy_loss       | -42        |\n", "|    explained_variance | 1.19e-07   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 5899       |\n", "|    policy_loss        | -94.3      |\n", "|    reward             | -1.7350562 |\n", "|    std                | 1.03       |\n", "|    value_loss         | 7.48       |\n", "--------------------------------------\n", "day: 3330, episode: 10\n", "begin_total_asset: 952508.66\n", "end_total_asset: 4088694.53\n", "total_reward: 3136185.87\n", "total_cost: 3157.22\n", "total_trades: 58734\n", "Sharpe: 0.733\n", "=================================\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 6000      |\n", "|    time_elapsed       | 507       |\n", "|    total_timesteps    | 30000     |\n", "| train/                |           |\n", "|    entropy_loss       | -42       |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 5999      |\n", "|    policy_loss        | 9.33      |\n", "|    reward             | 1.7072018 |\n", "|    std                | 1.03      |\n", "|    value_loss         | 0.168     |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 6100       |\n", "|    time_elapsed       | 515        |\n", "|    total_timesteps    | 30500      |\n", "| train/                |            |\n", "|    entropy_loss       | -42        |\n", "|    explained_variance | 0.137      |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 6099       |\n", "|    policy_loss        | 86.1       |\n", "|    reward             | 0.23781453 |\n", "|    std                | 1.03       |\n", "|    value_loss         | 5.84       |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 59          |\n", "|    iterations         | 6200        |\n", "|    time_elapsed       | 522         |\n", "|    total_timesteps    | 31000       |\n", "| train/                |             |\n", "|    entropy_loss       | -42         |\n", "|    explained_variance | 1.19e-07    |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 6199        |\n", "|    policy_loss        | 81.6        |\n", "|    reward             | -0.55448675 |\n", "|    std                | 1.03        |\n", "|    value_loss         | 4.51        |\n", "---------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 6300       |\n", "|    time_elapsed       | 532        |\n", "|    total_timesteps    | 31500      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.1      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 6299       |\n", "|    policy_loss        | -123       |\n", "|    reward             | 0.53070265 |\n", "|    std                | 1.03       |\n", "|    value_loss         | 10.1       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 6400       |\n", "|    time_elapsed       | 539        |\n", "|    total_timesteps    | 32000      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.2      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 6399       |\n", "|    policy_loss        | -35.1      |\n", "|    reward             | -0.7190698 |\n", "|    std                | 1.04       |\n", "|    value_loss         | 0.746      |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 59          |\n", "|    iterations         | 6500        |\n", "|    time_elapsed       | 547         |\n", "|    total_timesteps    | 32500       |\n", "| train/                |             |\n", "|    entropy_loss       | -42.1       |\n", "|    explained_variance | -1.19e-07   |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 6499        |\n", "|    policy_loss        | -195        |\n", "|    reward             | -0.20805828 |\n", "|    std                | 1.04        |\n", "|    value_loss         | 24.3        |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 6600      |\n", "|    time_elapsed       | 557       |\n", "|    total_timesteps    | 33000     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.1     |\n", "|    explained_variance | 0.0285    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 6599      |\n", "|    policy_loss        | -113      |\n", "|    reward             | -2.668644 |\n", "|    std                | 1.04      |\n", "|    value_loss         | 13.9      |\n", "-------------------------------------\n", "----------------------------------------\n", "| time/                 |              |\n", "|    fps                | 59           |\n", "|    iterations         | 6700         |\n", "|    time_elapsed       | 563          |\n", "|    total_timesteps    | 33500        |\n", "| train/                |              |\n", "|    entropy_loss       | -42.2        |\n", "|    explained_variance | -0.603       |\n", "|    learning_rate      | 0.0007       |\n", "|    n_updates          | 6699         |\n", "|    policy_loss        | -39.6        |\n", "|    reward             | -0.083356254 |\n", "|    std                | 1.04         |\n", "|    value_loss         | 0.818        |\n", "----------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 6800      |\n", "|    time_elapsed       | 572       |\n", "|    total_timesteps    | 34000     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.2     |\n", "|    explained_variance | 0.0184    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 6799      |\n", "|    policy_loss        | 86.5      |\n", "|    reward             | 0.6618178 |\n", "|    std                | 1.04      |\n", "|    value_loss         | 5.35      |\n", "-------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 59          |\n", "|    iterations         | 6900        |\n", "|    time_elapsed       | 581         |\n", "|    total_timesteps    | 34500       |\n", "| train/                |             |\n", "|    entropy_loss       | -42.2       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 6899        |\n", "|    policy_loss        | 56.7        |\n", "|    reward             | 0.052872755 |\n", "|    std                | 1.04        |\n", "|    value_loss         | 2.85        |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 7000      |\n", "|    time_elapsed       | 587       |\n", "|    total_timesteps    | 35000     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.3     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 6999      |\n", "|    policy_loss        | 197       |\n", "|    reward             | 1.6442178 |\n", "|    std                | 1.04      |\n", "|    value_loss         | 26.3      |\n", "-------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 59          |\n", "|    iterations         | 7100        |\n", "|    time_elapsed       | 597         |\n", "|    total_timesteps    | 35500       |\n", "| train/                |             |\n", "|    entropy_loss       | -42.3       |\n", "|    explained_variance | -0.0238     |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 7099        |\n", "|    policy_loss        | 39.7        |\n", "|    reward             | -0.16224274 |\n", "|    std                | 1.04        |\n", "|    value_loss         | 1.43        |\n", "---------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 7200       |\n", "|    time_elapsed       | 605        |\n", "|    total_timesteps    | 36000      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.3      |\n", "|    explained_variance | 0.03       |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 7199       |\n", "|    policy_loss        | 139        |\n", "|    reward             | -0.1674491 |\n", "|    std                | 1.04       |\n", "|    value_loss         | 11.7       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 7300      |\n", "|    time_elapsed       | 611       |\n", "|    total_timesteps    | 36500     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.4     |\n", "|    explained_variance | -0.0288   |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 7299      |\n", "|    policy_loss        | -406      |\n", "|    reward             | 2.2645469 |\n", "|    std                | 1.04      |\n", "|    value_loss         | 134       |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 7400       |\n", "|    time_elapsed       | 622        |\n", "|    total_timesteps    | 37000      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.4      |\n", "|    explained_variance | 0.0351     |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 7399       |\n", "|    policy_loss        | 73.6       |\n", "|    reward             | 0.30078474 |\n", "|    std                | 1.04       |\n", "|    value_loss         | 3.6        |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 7500       |\n", "|    time_elapsed       | 629        |\n", "|    total_timesteps    | 37500      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.3      |\n", "|    explained_variance | 5.96e-08   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 7499       |\n", "|    policy_loss        | -8.08      |\n", "|    reward             | -0.3665664 |\n", "|    std                | 1.04       |\n", "|    value_loss         | 0.214      |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 59          |\n", "|    iterations         | 7600        |\n", "|    time_elapsed       | 636         |\n", "|    total_timesteps    | 38000       |\n", "| train/                |             |\n", "|    entropy_loss       | -42.4       |\n", "|    explained_variance | -1.19e-07   |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 7599        |\n", "|    policy_loss        | 42.9        |\n", "|    reward             | -0.79383886 |\n", "|    std                | 1.04        |\n", "|    value_loss         | 1.9         |\n", "---------------------------------------\n", "----------------------------------------\n", "| time/                 |              |\n", "|    fps                | 59           |\n", "|    iterations         | 7700         |\n", "|    time_elapsed       | 647          |\n", "|    total_timesteps    | 38500        |\n", "| train/                |              |\n", "|    entropy_loss       | -42.4        |\n", "|    explained_variance | -1.19e-07    |\n", "|    learning_rate      | 0.0007       |\n", "|    n_updates          | 7699         |\n", "|    policy_loss        | -104         |\n", "|    reward             | -0.073217735 |\n", "|    std                | 1.05         |\n", "|    value_loss         | 10.3         |\n", "----------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 7800       |\n", "|    time_elapsed       | 653        |\n", "|    total_timesteps    | 39000      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.4      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 7799       |\n", "|    policy_loss        | -152       |\n", "|    reward             | -1.8329335 |\n", "|    std                | 1.05       |\n", "|    value_loss         | 16.7       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 7900       |\n", "|    time_elapsed       | 661        |\n", "|    total_timesteps    | 39500      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.4      |\n", "|    explained_variance | -1.19e-07  |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 7899       |\n", "|    policy_loss        | 144        |\n", "|    reward             | -0.8008484 |\n", "|    std                | 1.05       |\n", "|    value_loss         | 15.8       |\n", "--------------------------------------\n", "----------------------------------------\n", "| time/                 |              |\n", "|    fps                | 59           |\n", "|    iterations         | 8000         |\n", "|    time_elapsed       | 671          |\n", "|    total_timesteps    | 40000        |\n", "| train/                |              |\n", "|    entropy_loss       | -42.3        |\n", "|    explained_variance | 5.96e-08     |\n", "|    learning_rate      | 0.0007       |\n", "|    n_updates          | 7999         |\n", "|    policy_loss        | -8.53        |\n", "|    reward             | -0.031915538 |\n", "|    std                | 1.04         |\n", "|    value_loss         | 0.0835       |\n", "----------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 8100      |\n", "|    time_elapsed       | 677       |\n", "|    total_timesteps    | 40500     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.4     |\n", "|    explained_variance | 5.96e-08  |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 8099      |\n", "|    policy_loss        | -69.3     |\n", "|    reward             | 0.8095603 |\n", "|    std                | 1.04      |\n", "|    value_loss         | 3.08      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 8200       |\n", "|    time_elapsed       | 686        |\n", "|    total_timesteps    | 41000      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.3      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 8199       |\n", "|    policy_loss        | -5.2       |\n", "|    reward             | -0.5655167 |\n", "|    std                | 1.04       |\n", "|    value_loss         | 0.69       |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 8300       |\n", "|    time_elapsed       | 695        |\n", "|    total_timesteps    | 41500      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.4      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 8299       |\n", "|    policy_loss        | -29.8      |\n", "|    reward             | -1.5929188 |\n", "|    std                | 1.04       |\n", "|    value_loss         | 0.672      |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 59          |\n", "|    iterations         | 8400        |\n", "|    time_elapsed       | 701         |\n", "|    total_timesteps    | 42000       |\n", "| train/                |             |\n", "|    entropy_loss       | -42.4       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 8399        |\n", "|    policy_loss        | -54.2       |\n", "|    reward             | -0.53150016 |\n", "|    std                | 1.05        |\n", "|    value_loss         | 9.5         |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 8500      |\n", "|    time_elapsed       | 711       |\n", "|    total_timesteps    | 42500     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.5     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 8499      |\n", "|    policy_loss        | 237       |\n", "|    reward             | 2.7706447 |\n", "|    std                | 1.05      |\n", "|    value_loss         | 42.1      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 8600      |\n", "|    time_elapsed       | 719       |\n", "|    total_timesteps    | 43000     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.5     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 8599      |\n", "|    policy_loss        | -188      |\n", "|    reward             | 1.1153419 |\n", "|    std                | 1.05      |\n", "|    value_loss         | 21.7      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 8700       |\n", "|    time_elapsed       | 730        |\n", "|    total_timesteps    | 43500      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.4      |\n", "|    explained_variance | -1.19e-07  |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 8699       |\n", "|    policy_loss        | -17.4      |\n", "|    reward             | -0.5148427 |\n", "|    std                | 1.05       |\n", "|    value_loss         | 0.297      |\n", "--------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 8800       |\n", "|    time_elapsed       | 740        |\n", "|    total_timesteps    | 44000      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.4      |\n", "|    explained_variance | 1.19e-07   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 8799       |\n", "|    policy_loss        | 41.4       |\n", "|    reward             | 0.32814896 |\n", "|    std                | 1.05       |\n", "|    value_loss         | 1.6        |\n", "--------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 59          |\n", "|    iterations         | 8900        |\n", "|    time_elapsed       | 746         |\n", "|    total_timesteps    | 44500       |\n", "| train/                |             |\n", "|    entropy_loss       | -42.4       |\n", "|    explained_variance | 0           |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 8899        |\n", "|    policy_loss        | -47.2       |\n", "|    reward             | -0.17413093 |\n", "|    std                | 1.05        |\n", "|    value_loss         | 1.75        |\n", "---------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 9000       |\n", "|    time_elapsed       | 755        |\n", "|    total_timesteps    | 45000      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.4      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 8999       |\n", "|    policy_loss        | 65.1       |\n", "|    reward             | 0.38266626 |\n", "|    std                | 1.05       |\n", "|    value_loss         | 6.64       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 9100      |\n", "|    time_elapsed       | 764       |\n", "|    total_timesteps    | 45500     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.4     |\n", "|    explained_variance | -1.19e-07 |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 9099      |\n", "|    policy_loss        | 31.2      |\n", "|    reward             | 1.3317974 |\n", "|    std                | 1.05      |\n", "|    value_loss         | 0.927     |\n", "-------------------------------------\n", "---------------------------------------\n", "| time/                 |             |\n", "|    fps                | 59          |\n", "|    iterations         | 9200        |\n", "|    time_elapsed       | 770         |\n", "|    total_timesteps    | 46000       |\n", "| train/                |             |\n", "|    entropy_loss       | -42.4       |\n", "|    explained_variance | -0.0927     |\n", "|    learning_rate      | 0.0007      |\n", "|    n_updates          | 9199        |\n", "|    policy_loss        | 181         |\n", "|    reward             | -0.49035767 |\n", "|    std                | 1.05        |\n", "|    value_loss         | 21.3        |\n", "---------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 9300      |\n", "|    time_elapsed       | 780       |\n", "|    total_timesteps    | 46500     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.5     |\n", "|    explained_variance | 1.19e-07  |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 9299      |\n", "|    policy_loss        | 148       |\n", "|    reward             | -8.756936 |\n", "|    std                | 1.05      |\n", "|    value_loss         | 30.5      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 9400      |\n", "|    time_elapsed       | 788       |\n", "|    total_timesteps    | 47000     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.5     |\n", "|    explained_variance | -0.066    |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 9399      |\n", "|    policy_loss        | 40.5      |\n", "|    reward             | 0.5117786 |\n", "|    std                | 1.05      |\n", "|    value_loss         | 1.51      |\n", "-------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 9500      |\n", "|    time_elapsed       | 794       |\n", "|    total_timesteps    | 47500     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.5     |\n", "|    explained_variance | 0         |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 9499      |\n", "|    policy_loss        | 46.4      |\n", "|    reward             | 1.5631902 |\n", "|    std                | 1.05      |\n", "|    value_loss         | 1.37      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 9600       |\n", "|    time_elapsed       | 804        |\n", "|    total_timesteps    | 48000      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.5      |\n", "|    explained_variance | 5.96e-08   |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 9599       |\n", "|    policy_loss        | 4.73       |\n", "|    reward             | -0.8106855 |\n", "|    std                | 1.05       |\n", "|    value_loss         | 0.346      |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 9700      |\n", "|    time_elapsed       | 811       |\n", "|    total_timesteps    | 48500     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.5     |\n", "|    explained_variance | -1.19e-07 |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 9699      |\n", "|    policy_loss        | 60.8      |\n", "|    reward             | 1.219504  |\n", "|    std                | 1.05      |\n", "|    value_loss         | 3.44      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 9800       |\n", "|    time_elapsed       | 818        |\n", "|    total_timesteps    | 49000      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.5      |\n", "|    explained_variance | 0.00147    |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 9799       |\n", "|    policy_loss        | -19        |\n", "|    reward             | 0.36547118 |\n", "|    std                | 1.05       |\n", "|    value_loss         | 6.68       |\n", "--------------------------------------\n", "-------------------------------------\n", "| time/                 |           |\n", "|    fps                | 59        |\n", "|    iterations         | 9900      |\n", "|    time_elapsed       | 829       |\n", "|    total_timesteps    | 49500     |\n", "| train/                |           |\n", "|    entropy_loss       | -42.5     |\n", "|    explained_variance | -0.0611   |\n", "|    learning_rate      | 0.0007    |\n", "|    n_updates          | 9899      |\n", "|    policy_loss        | -14       |\n", "|    reward             | 1.2229353 |\n", "|    std                | 1.05      |\n", "|    value_loss         | 2.29      |\n", "-------------------------------------\n", "--------------------------------------\n", "| time/                 |            |\n", "|    fps                | 59         |\n", "|    iterations         | 10000      |\n", "|    time_elapsed       | 835        |\n", "|    total_timesteps    | 50000      |\n", "| train/                |            |\n", "|    entropy_loss       | -42.6      |\n", "|    explained_variance | 0          |\n", "|    learning_rate      | 0.0007     |\n", "|    n_updates          | 9999       |\n", "|    policy_loss        | -15.6      |\n", "|    reward             | 0.31784078 |\n", "|    std                | 1.05       |\n", "|    value_loss         | 0.296      |\n", "--------------------------------------\n", "{'batch_size': 128, 'buffer_size': 50000, 'learning_rate': 0.001}\n", "Using cpu device\n", "Logging to results/ddpg\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 4          |\n", "|    fps             | 23         |\n", "|    time_elapsed    | 556        |\n", "|    total_timesteps | 13324      |\n", "| train/             |            |\n", "|    actor_loss      | 20.3       |\n", "|    critic_loss     | 66.8       |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 9993       |\n", "|    reward          | -4.5011277 |\n", "-----------------------------------\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 8          |\n", "|    fps             | 21         |\n", "|    time_elapsed    | 1250       |\n", "|    total_timesteps | 26648      |\n", "| train/             |            |\n", "|    actor_loss      | 2.62       |\n", "|    critic_loss     | 9.79       |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 23317      |\n", "|    reward          | -4.5011277 |\n", "-----------------------------------\n", "day: 3330, episode: 10\n", "begin_total_asset: 965326.95\n", "end_total_asset: 3940368.63\n", "total_reward: 2975041.68\n", "total_cost: 964.36\n", "total_trades: 53280\n", "Sharpe: 0.657\n", "=================================\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 12         |\n", "|    fps             | 20         |\n", "|    time_elapsed    | 1944       |\n", "|    total_timesteps | 39972      |\n", "| train/             |            |\n", "|    actor_loss      | -3.63      |\n", "|    critic_loss     | 2.39       |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 36641      |\n", "|    reward          | -4.5011277 |\n", "-----------------------------------\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 16         |\n", "|    fps             | 20         |\n", "|    time_elapsed    | 2656       |\n", "|    total_timesteps | 53296      |\n", "| train/             |            |\n", "|    actor_loss      | -6.92      |\n", "|    critic_loss     | 1.51       |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 49965      |\n", "|    reward          | -4.5011277 |\n", "-----------------------------------\n", "{'n_steps': 2048, 'ent_coef': 0.01, 'learning_rate': 0.00025, 'batch_size': 128}\n", "Using cpu device\n", "Logging to results/ppo\n", "-----------------------------------\n", "| time/              |            |\n", "|    fps             | 70         |\n", "|    iterations      | 1          |\n", "|    time_elapsed    | 29         |\n", "|    total_timesteps | 2048       |\n", "| train/             |            |\n", "|    reward          | -0.3290882 |\n", "-----------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 67          |\n", "|    iterations           | 2           |\n", "|    time_elapsed         | 60          |\n", "|    total_timesteps      | 4096        |\n", "| train/                  |             |\n", "|    approx_kl            | 0.019916927 |\n", "|    clip_fraction        | 0.207       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.2       |\n", "|    explained_variance   | -0.00611    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 6.42        |\n", "|    n_updates            | 10          |\n", "|    policy_gradient_loss | -0.0268     |\n", "|    reward               | 0.84259444  |\n", "|    std                  | 1           |\n", "|    value_loss           | 15          |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 65          |\n", "|    iterations           | 3           |\n", "|    time_elapsed         | 93          |\n", "|    total_timesteps      | 6144        |\n", "| train/                  |             |\n", "|    approx_kl            | 0.016416349 |\n", "|    clip_fraction        | 0.211       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.3       |\n", "|    explained_variance   | 0.00243     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 71.4        |\n", "|    n_updates            | 20          |\n", "|    policy_gradient_loss | -0.0189     |\n", "|    reward               | -22.102169  |\n", "|    std                  | 1.01        |\n", "|    value_loss           | 95.2        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 65          |\n", "|    iterations           | 4           |\n", "|    time_elapsed         | 125         |\n", "|    total_timesteps      | 8192        |\n", "| train/                  |             |\n", "|    approx_kl            | 0.016711425 |\n", "|    clip_fraction        | 0.152       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.3       |\n", "|    explained_variance   | -0.0235     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 19.2        |\n", "|    n_updates            | 30          |\n", "|    policy_gradient_loss | -0.0181     |\n", "|    reward               | 0.8641611   |\n", "|    std                  | 1.01        |\n", "|    value_loss           | 51          |\n", "-----------------------------------------\n", "----------------------------------------\n", "| time/                   |            |\n", "|    fps                  | 64         |\n", "|    iterations           | 5          |\n", "|    time_elapsed         | 158        |\n", "|    total_timesteps      | 10240      |\n", "| train/                  |            |\n", "|    approx_kl            | 0.02179965 |\n", "|    clip_fraction        | 0.258      |\n", "|    clip_range           | 0.2        |\n", "|    entropy_loss         | -41.3      |\n", "|    explained_variance   | -0.00376   |\n", "|    learning_rate        | 0.00025    |\n", "|    loss                 | 24.8       |\n", "|    n_updates            | 40         |\n", "|    policy_gradient_loss | -0.0161    |\n", "|    reward               | 0.7124557  |\n", "|    std                  | 1.01       |\n", "|    value_loss           | 37.7       |\n", "----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 6           |\n", "|    time_elapsed         | 189         |\n", "|    total_timesteps      | 12288       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.020254686 |\n", "|    clip_fraction        | 0.206       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.4       |\n", "|    explained_variance   | -0.02       |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 15.9        |\n", "|    n_updates            | 50          |\n", "|    policy_gradient_loss | -0.0192     |\n", "|    reward               | 2.9676142   |\n", "|    std                  | 1.01        |\n", "|    value_loss           | 56          |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 7           |\n", "|    time_elapsed         | 221         |\n", "|    total_timesteps      | 14336       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.015349641 |\n", "|    clip_fraction        | 0.182       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.5       |\n", "|    explained_variance   | 0.00714     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 7.18        |\n", "|    n_updates            | 60          |\n", "|    policy_gradient_loss | -0.0222     |\n", "|    reward               | -1.0227845  |\n", "|    std                  | 1.01        |\n", "|    value_loss           | 12.5        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 8           |\n", "|    time_elapsed         | 254         |\n", "|    total_timesteps      | 16384       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.020761559 |\n", "|    clip_fraction        | 0.231       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.5       |\n", "|    explained_variance   | -0.00857    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 25.2        |\n", "|    n_updates            | 70          |\n", "|    policy_gradient_loss | -0.0199     |\n", "|    reward               | 0.80425155  |\n", "|    std                  | 1.01        |\n", "|    value_loss           | 57.8        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 9           |\n", "|    time_elapsed         | 283         |\n", "|    total_timesteps      | 18432       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.018122694 |\n", "|    clip_fraction        | 0.236       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.5       |\n", "|    explained_variance   | 0.00296     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 28.1        |\n", "|    n_updates            | 80          |\n", "|    policy_gradient_loss | -0.0166     |\n", "|    reward               | -1.42386    |\n", "|    std                  | 1.01        |\n", "|    value_loss           | 57.8        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 10          |\n", "|    time_elapsed         | 318         |\n", "|    total_timesteps      | 20480       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.022673171 |\n", "|    clip_fraction        | 0.205       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.6       |\n", "|    explained_variance   | -0.013      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 17.3        |\n", "|    n_updates            | 90          |\n", "|    policy_gradient_loss | -0.0191     |\n", "|    reward               | 0.8197509   |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 44.3        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 11          |\n", "|    time_elapsed         | 352         |\n", "|    total_timesteps      | 22528       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.020850785 |\n", "|    clip_fraction        | 0.214       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.6       |\n", "|    explained_variance   | -0.00669    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 48.4        |\n", "|    n_updates            | 100         |\n", "|    policy_gradient_loss | -0.0161     |\n", "|    reward               | 1.2033767   |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 99.1        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 12          |\n", "|    time_elapsed         | 384         |\n", "|    total_timesteps      | 24576       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.024814304 |\n", "|    clip_fraction        | 0.251       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.6       |\n", "|    explained_variance   | -0.0225     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 10.8        |\n", "|    n_updates            | 110         |\n", "|    policy_gradient_loss | -0.018      |\n", "|    reward               | 1.610058    |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 22.5        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 13          |\n", "|    time_elapsed         | 416         |\n", "|    total_timesteps      | 26624       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.017855735 |\n", "|    clip_fraction        | 0.173       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.7       |\n", "|    explained_variance   | 0.00501     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 34.5        |\n", "|    n_updates            | 120         |\n", "|    policy_gradient_loss | -0.0189     |\n", "|    reward               | 7.162905    |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 112         |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 14          |\n", "|    time_elapsed         | 446         |\n", "|    total_timesteps      | 28672       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.018644353 |\n", "|    clip_fraction        | 0.153       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.7       |\n", "|    explained_variance   | 0.0117      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 16.7        |\n", "|    n_updates            | 130         |\n", "|    policy_gradient_loss | -0.0172     |\n", "|    reward               | 2.0473788   |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 53.3        |\n", "-----------------------------------------\n", "day: 3330, episode: 10\n", "begin_total_asset: 994554.41\n", "end_total_asset: 4699503.39\n", "total_reward: 3704948.98\n", "total_cost: 439274.68\n", "total_trades: 90096\n", "Sharpe: 0.806\n", "=================================\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 15          |\n", "|    time_elapsed         | 480         |\n", "|    total_timesteps      | 30720       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.02508668  |\n", "|    clip_fraction        | 0.25        |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.8       |\n", "|    explained_variance   | -0.0505     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 4.42        |\n", "|    n_updates            | 140         |\n", "|    policy_gradient_loss | -0.0173     |\n", "|    reward               | -0.36127353 |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 14.8        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 16          |\n", "|    time_elapsed         | 510         |\n", "|    total_timesteps      | 32768       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.021448491 |\n", "|    clip_fraction        | 0.211       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.8       |\n", "|    explained_variance   | 0.00132     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 38          |\n", "|    n_updates            | 150         |\n", "|    policy_gradient_loss | -0.00894    |\n", "|    reward               | -2.4289682  |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 88          |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 17          |\n", "|    time_elapsed         | 542         |\n", "|    total_timesteps      | 34816       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.02103462  |\n", "|    clip_fraction        | 0.208       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.8       |\n", "|    explained_variance   | -0.0246     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 35.3        |\n", "|    n_updates            | 160         |\n", "|    policy_gradient_loss | -0.0134     |\n", "|    reward               | -0.71985894 |\n", "|    std                  | 1.02        |\n", "|    value_loss           | 54.5        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 18          |\n", "|    time_elapsed         | 577         |\n", "|    total_timesteps      | 36864       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.022089712 |\n", "|    clip_fraction        | 0.213       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.9       |\n", "|    explained_variance   | -0.0028     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 27.9        |\n", "|    n_updates            | 170         |\n", "|    policy_gradient_loss | -0.0207     |\n", "|    reward               | 0.11034006  |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 39.3        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 19          |\n", "|    time_elapsed         | 609         |\n", "|    total_timesteps      | 38912       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.014264661 |\n", "|    clip_fraction        | 0.126       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -41.9       |\n", "|    explained_variance   | -0.00283    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 58.6        |\n", "|    n_updates            | 180         |\n", "|    policy_gradient_loss | -0.0135     |\n", "|    reward               | 6.176509    |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 119         |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 20          |\n", "|    time_elapsed         | 642         |\n", "|    total_timesteps      | 40960       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.027180977 |\n", "|    clip_fraction        | 0.292       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42         |\n", "|    explained_variance   | 0.0421      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 8.9         |\n", "|    n_updates            | 190         |\n", "|    policy_gradient_loss | -0.0156     |\n", "|    reward               | 0.20096779  |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 19.8        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 21          |\n", "|    time_elapsed         | 671         |\n", "|    total_timesteps      | 43008       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.021884244 |\n", "|    clip_fraction        | 0.205       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42         |\n", "|    explained_variance   | -0.00219    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 53.4        |\n", "|    n_updates            | 200         |\n", "|    policy_gradient_loss | -0.0145     |\n", "|    reward               | -0.839949   |\n", "|    std                  | 1.03        |\n", "|    value_loss           | 94.3        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 22          |\n", "|    time_elapsed         | 706         |\n", "|    total_timesteps      | 45056       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.024635753 |\n", "|    clip_fraction        | 0.235       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.1       |\n", "|    explained_variance   | -0.00329    |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 27.6        |\n", "|    n_updates            | 210         |\n", "|    policy_gradient_loss | -0.0148     |\n", "|    reward               | -0.21918707 |\n", "|    std                  | 1.04        |\n", "|    value_loss           | 61.8        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 64          |\n", "|    iterations           | 23          |\n", "|    time_elapsed         | 735         |\n", "|    total_timesteps      | 47104       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.038902897 |\n", "|    clip_fraction        | 0.28        |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.2       |\n", "|    explained_variance   | -0.0241     |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 21.9        |\n", "|    n_updates            | 220         |\n", "|    policy_gradient_loss | -0.0178     |\n", "|    reward               | -0.12725857 |\n", "|    std                  | 1.04        |\n", "|    value_loss           | 34.3        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 24          |\n", "|    time_elapsed         | 768         |\n", "|    total_timesteps      | 49152       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.017998032 |\n", "|    clip_fraction        | 0.174       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.2       |\n", "|    explained_variance   | 0.0111      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 27.9        |\n", "|    n_updates            | 230         |\n", "|    policy_gradient_loss | -0.0148     |\n", "|    reward               | 1.7231001   |\n", "|    std                  | 1.04        |\n", "|    value_loss           | 65.2        |\n", "-----------------------------------------\n", "-----------------------------------------\n", "| time/                   |             |\n", "|    fps                  | 63          |\n", "|    iterations           | 25          |\n", "|    time_elapsed         | 804         |\n", "|    total_timesteps      | 51200       |\n", "| train/                  |             |\n", "|    approx_kl            | 0.017844416 |\n", "|    clip_fraction        | 0.186       |\n", "|    clip_range           | 0.2         |\n", "|    entropy_loss         | -42.3       |\n", "|    explained_variance   | 0.0211      |\n", "|    learning_rate        | 0.00025     |\n", "|    loss                 | 13.5        |\n", "|    n_updates            | 240         |\n", "|    policy_gradient_loss | -0.0149     |\n", "|    reward               | -1.0208522  |\n", "|    std                  | 1.04        |\n", "|    value_loss           | 35.2        |\n", "-----------------------------------------\n", "{'batch_size': 128, 'buffer_size': 100000, 'learning_rate': 0.0001, 'learning_starts': 100, 'ent_coef': 'auto_0.1'}\n", "Using cpu device\n", "Logging to results/sac\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 4          |\n", "|    fps             | 18         |\n", "|    time_elapsed    | 704        |\n", "|    total_timesteps | 13324      |\n", "| train/             |            |\n", "|    actor_loss      | 1.1e+03    |\n", "|    critic_loss     | 642        |\n", "|    ent_coef        | 0.169      |\n", "|    ent_coef_loss   | -83.1      |\n", "|    learning_rate   | 0.0001     |\n", "|    n_updates       | 13223      |\n", "|    reward          | -4.2128644 |\n", "-----------------------------------\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 8          |\n", "|    fps             | 18         |\n", "|    time_elapsed    | 1433       |\n", "|    total_timesteps | 26648      |\n", "| train/             |            |\n", "|    actor_loss      | 451        |\n", "|    critic_loss     | 27.5       |\n", "|    ent_coef        | 0.046      |\n", "|    ent_coef_loss   | -109       |\n", "|    learning_rate   | 0.0001     |\n", "|    n_updates       | 26547      |\n", "|    reward          | -4.2404695 |\n", "-----------------------------------\n", "day: 3330, episode: 10\n", "begin_total_asset: 953106.81\n", "end_total_asset: 7458866.64\n", "total_reward: 6505759.83\n", "total_cost: 8648.15\n", "total_trades: 59083\n", "Sharpe: 0.842\n", "=================================\n", "----------------------------------\n", "| time/              |           |\n", "|    episodes        | 12        |\n", "|    fps             | 18        |\n", "|    time_elapsed    | 2152      |\n", "|    total_timesteps | 39972     |\n", "| train/             |           |\n", "|    actor_loss      | 216       |\n", "|    critic_loss     | 38.6      |\n", "|    ent_coef        | 0.0127    |\n", "|    ent_coef_loss   | -102      |\n", "|    learning_rate   | 0.0001    |\n", "|    n_updates       | 39871     |\n", "|    reward          | -3.931381 |\n", "----------------------------------\n", "{'batch_size': 100, 'buffer_size': 1000000, 'learning_rate': 0.001}\n", "Using cpu device\n", "Logging to results/td3\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 4          |\n", "|    fps             | 25         |\n", "|    time_elapsed    | 526        |\n", "|    total_timesteps | 13324      |\n", "| train/             |            |\n", "|    actor_loss      | 91.6       |\n", "|    critic_loss     | 1.45e+03   |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 9993       |\n", "|    reward          | -3.5290053 |\n", "-----------------------------------\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 8          |\n", "|    fps             | 22         |\n", "|    time_elapsed    | 1191       |\n", "|    total_timesteps | 26648      |\n", "| train/             |            |\n", "|    actor_loss      | 43.8       |\n", "|    critic_loss     | 317        |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 23317      |\n", "|    reward          | -3.5290053 |\n", "-----------------------------------\n", "day: 3330, episode: 10\n", "begin_total_asset: 972865.93\n", "end_total_asset: 3563567.55\n", "total_reward: 2590701.62\n", "total_cost: 971.89\n", "total_trades: 46620\n", "Sharpe: 0.648\n", "=================================\n", "-----------------------------------\n", "| time/              |            |\n", "|    episodes        | 12         |\n", "|    fps             | 21         |\n", "|    time_elapsed    | 1862       |\n", "|    total_timesteps | 39972      |\n", "| train/             |            |\n", "|    actor_loss      | 34.3       |\n", "|    critic_loss     | 54.4       |\n", "|    learning_rate   | 0.001      |\n", "|    n_updates       | 36641      |\n", "|    reward          | -3.5290053 |\n", "-----------------------------------\n"]}], "source": ["train_start_date = \"2009-01-01\"\n", "train_end_date = \"2022-09-01\"\n", "trade_start_date = \"2022-09-01\"\n", "trade_end_date = \"2023-11-01\"\n", "if_store_actions = True\n", "if_store_result = True\n", "if_using_a2c = True\n", "if_using_ddpg = True\n", "if_using_ppo = True\n", "if_using_sac = True\n", "if_using_td3 = True\n", "\n", "stock_trading(\n", "    train_start_date=train_start_date,\n", "    train_end_date=train_end_date,\n", "    trade_start_date=trade_start_date,\n", "    trade_end_date=trade_end_date,\n", "    if_store_actions=if_store_actions,\n", "    if_store_result=if_store_result,\n", "    if_using_a2c=if_using_a2c,\n", "    if_using_ddpg=if_using_ddpg,\n", "    if_using_ppo=if_using_ppo,\n", "    if_using_sac=if_using_sac,\n", "    if_using_td3=if_using_td3,\n", ")\n", "\n"]}], "metadata": {"colab": {"collapsed_sections": ["HMNR5nHjh1iz", "uijiWgkuh1jB", "MRiOtrywfAo1", "_gDkU-j-fCmZ", "3Zpv4S0-fDBv"], "provenance": []}, "kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5 (default, Sep  4 2020, 02:22:02) \n[Clang 10.0.0 ]"}, "vscode": {"interpreter": {"hash": "54cefccbf0f07c9750f12aa115c023dfa5ed4acecf9e7ad3bc9391869be60d0c"}}}, "nbformat": 4, "nbformat_minor": 0}