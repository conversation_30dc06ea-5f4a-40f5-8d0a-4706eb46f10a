:github_url: https://github.com/AI4Finance-Foundation/FinRL

First Glance
============================

To quickly understand what is FinRL and how it works, you can go through the series Stock_NeurIPS2018, including *Stock_NeurIPS2018_Data.ipynb*, *Stock_NeurIPS2018_Train.ipynb*, *Stock_NeurIPS2018_Backtest.ipynb* in our examples directory (https://github.com/AI4Finance-Foundation/FinRL/tree/master/examples)

This is how we use Deep Reinforcement Learning for Stock Trading from scratch.


.. tip::

    Run the code step by step at `Google Colab`_.

    .. _Google Colab: https://colab.research.google.com/github/AI4Finance-Foundation/FinRL-Tutorials/blob/master/1-Introduction/Stock_NeurIPS2018_SB3.ipynb

The notebook and the following result is based on our paper *Practical deep reinforcement learning approach for stock trading* <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. "Practical deep reinforcement learning approach for stock trading." arXiv preprint arXiv:1811.07522 (2018).

.. image:: ../image/result_NeurIPS.png
   :width: 80%
   :align: center
