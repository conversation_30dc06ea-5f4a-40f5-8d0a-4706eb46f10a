{"cells": [{"cell_type": "markdown", "metadata": {"id": "3xt6fIDownZs"}, "source": ["# A guide Portfolio Optimization Environment\n", "\n", "This notebook aims to provide an example of using PortfolioOptimizationEnv (or POE) to train a reinforcement learning model that learns to solve the portfolio optimization problem.\n", "\n", "In this document, we will reproduce a famous architecture called EIIE (ensemble of identical independent evaluators), introduced in the following paper:\n", "\n", "- <PERSON><PERSON><PERSON>, <PERSON><PERSON>, & <PERSON><PERSON>. (2017). A Deep Reinforcement Learning Framework for the Financial Portfolio Management Problem. https://doi.org/10.48550/arXiv.1706.10059.\n", "\n", "It's advisable to read it to understand the algorithm implemented in this notebook.\n", "\n", "### Note\n", "If you're using this environment, consider citing the following paper (in adittion to FinRL references):\n", "\n", "- <PERSON><PERSON><PERSON>, & <PERSON> (2023). POE: A General Portfolio Optimization Environment for FinRL. In *Anais do II Brazilian Workshop on Artificial Intelligence in Finance* (pp. 132–143). SBC. https://doi.org/10.5753/bwaif.2023.231144.\n", "\n", "```\n", "@inproceedings{bwaif,\n", " author = {<PERSON><PERSON><PERSON> and <PERSON>},\n", " title = {POE: A General Portfolio Optimization Environment for FinRL},\n", " booktitle = {Anais do <PERSON> Brazilian Workshop on Artificial Intelligence in Finance},\n", " location = {João <PERSON>/PB},\n", " year = {2023},\n", " keywords = {},\n", " issn = {0000-0000},\n", " pages = {132--143},\n", " publisher = {SBC},\n", " address = {Porto Alegre, RS, Brasil},\n", " doi = {10.5753/bwaif.2023.231144},\n", " url = {https://sol.sbc.org.br/index.php/bwaif/article/view/24959}\n", "}\n", "\n", "```"]}, {"cell_type": "markdown", "metadata": {"id": "Q0L7FZeWMUHp"}, "source": ["## Installation and imports\n", "\n", "To run this notebook in google colab, uncomment the cells below."]}, {"cell_type": "code", "execution_count": 41, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XGHfTt1HMVQw", "outputId": "e5226807-a740-4f22-a279-f466886518ba"}, "outputs": [], "source": ["## install finrl library\n", "# !sudo apt install swig\n", "# !pip install git+https://github.com/AI4Finance-Foundation/FinRL.git"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-GLganWiMYZ1", "outputId": "b3a7f99c-55dd-4274-c1ce-ab3a8111929a"}, "outputs": [], "source": ["## We also need to install quantstats, because the environment uses it to plot graphs\n", "# !pip install quantstats"]}, {"cell_type": "code", "execution_count": 43, "metadata": {"id": "6RqrzokqoanP"}, "outputs": [], "source": ["## Hide matplotlib warnings\n", "# import warnings\n", "# warnings.filterwarnings('ignore')\n", "\n", "import logging\n", "logging.getLogger('matplotlib.font_manager').disabled = True"]}, {"cell_type": "markdown", "metadata": {"id": "Cz8DLleGz_TF"}, "source": ["#### Import the necessary code libraries"]}, {"cell_type": "code", "execution_count": 44, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cP5t6U7-nYoc", "outputId": "fd138d3e-222a-4ec5-e008-03a28b89dae9"}, "outputs": [], "source": ["import torch\n", "\n", "from sklearn.preprocessing import MaxAbsScaler\n", "\n", "from finrl.meta.preprocessor.yahoodownloader import YahooDownloader\n", "from finrl.meta.preprocessor.preprocessors import GroupByScaler\n", "from finrl.meta.env_portfolio_optimization.env_portfolio_optimization import PortfolioOptimizationEnv\n", "from finrl.agents.portfolio_optimization.models import DRLAgent\n", "from finrl.agents.portfolio_optimization.architectures import EIIE\n", "\n", "device = 'cuda:0' if torch.cuda.is_available() else 'cpu'"]}, {"cell_type": "markdown", "metadata": {"id": "TY2yhvpASEyo"}, "source": ["## Fetch data\n", "\n", "In his paper, *<PERSON> et al* creates a portfolio composed by the top-11 cryptocurrencies based on 30-days volume. Since it's not specified when this classification was done, it's difficult to reproduce, so we will use a similar approach in the Brazillian stock market:\n", "\n", "- We select top-10 stocks from Brazillian stock market;\n", "- For simplicity, we disconsider stocks that have missing data for the days in period 2011-01-01 to 2019-12-31 (9 years);"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "H11UjCstSFwm", "outputId": "3d27b983-d1e0-41af-b20a-421be40e469f"}, "outputs": [], "source": ["TOP_BRL = [\n", "    \"VALE3.SA\", \"PETR4.SA\", \"ITUB4.SA\", \"BBDC4.SA\",\n", "    \"BBAS3.SA\", \"RENT3.SA\", \"LREN3.SA\", \"PRIO3.SA\",\n", "    \"WEGE3.SA\", \"ABEV3.SA\"\n", "]"]}, {"cell_type": "code", "execution_count": 46, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 623}, "id": "Bkm96aNsSIji", "outputId": "e3a20095-841e-4c89-c08e-24b9575cfb02"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[*********************100%%**********************]  1 of 1 completed\n", "[*********************100%%**********************]  1 of 1 completed\n", "[*********************100%%**********************]  1 of 1 completed\n", "[*********************100%%**********************]  1 of 1 completed"]}, {"name": "stdout", "output_type": "stream", "text": ["10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[*********************100%%**********************]  1 of 1 completed\n", "[*********************100%%**********************]  1 of 1 completed\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[*********************100%%**********************]  1 of 1 completed\n", "[*********************100%%**********************]  1 of 1 completed\n", "[*********************100%%**********************]  1 of 1 completed\n", "[*********************100%%**********************]  1 of 1 completed\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Shape of DataFrame:  (29780, 8)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>tic</th>\n", "      <th>day</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2011-01-03</td>\n", "      <td>8.632311</td>\n", "      <td>8.728203</td>\n", "      <td>8.630313</td>\n", "      <td>5.265024</td>\n", "      <td>576145</td>\n", "      <td>ABEV3.SA</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2011-01-03</td>\n", "      <td>15.750000</td>\n", "      <td>15.900000</td>\n", "      <td>15.690000</td>\n", "      <td>2.838269</td>\n", "      <td>6626800</td>\n", "      <td>BBAS3.SA</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2011-01-03</td>\n", "      <td>11.809763</td>\n", "      <td>11.927362</td>\n", "      <td>11.724237</td>\n", "      <td>6.688100</td>\n", "      <td>10862336</td>\n", "      <td>BBDC4.SA</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2011-01-03</td>\n", "      <td>18.031555</td>\n", "      <td>18.250118</td>\n", "      <td>17.963253</td>\n", "      <td>10.171988</td>\n", "      <td>10014663</td>\n", "      <td>ITUB4.SA</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2011-01-03</td>\n", "      <td>8.422694</td>\n", "      <td>8.629907</td>\n", "      <td>8.422694</td>\n", "      <td>6.294305</td>\n", "      <td>3652542</td>\n", "      <td>LREN3.SA</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29775</th>\n", "      <td>2022-12-29</td>\n", "      <td>24.990000</td>\n", "      <td>25.049999</td>\n", "      <td>24.430000</td>\n", "      <td>18.930288</td>\n", "      <td>57901800</td>\n", "      <td>PETR4.SA</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29776</th>\n", "      <td>2022-12-29</td>\n", "      <td>37.250000</td>\n", "      <td>37.480000</td>\n", "      <td>36.619999</td>\n", "      <td>37.150330</td>\n", "      <td>7890300</td>\n", "      <td>PRIO3.SA</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29777</th>\n", "      <td>2022-12-29</td>\n", "      <td>54.300869</td>\n", "      <td>55.776001</td>\n", "      <td>52.626392</td>\n", "      <td>51.326015</td>\n", "      <td>8786791</td>\n", "      <td>RENT3.SA</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29778</th>\n", "      <td>2022-12-29</td>\n", "      <td>89.000000</td>\n", "      <td>89.949997</td>\n", "      <td>88.830002</td>\n", "      <td>80.014046</td>\n", "      <td>40110800</td>\n", "      <td>VALE3.SA</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29779</th>\n", "      <td>2022-12-29</td>\n", "      <td>38.840000</td>\n", "      <td>38.990002</td>\n", "      <td>38.250000</td>\n", "      <td>37.525452</td>\n", "      <td>6444500</td>\n", "      <td>WEGE3.SA</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>29780 rows × 8 columns</p>\n", "</div>"], "text/plain": ["             date       open       high        low      close    volume  \\\n", "0      2011-01-03   8.632311   8.728203   8.630313   5.265024    576145   \n", "1      2011-01-03  15.750000  15.900000  15.690000   2.838269   6626800   \n", "2      2011-01-03  11.809763  11.927362  11.724237   6.688100  10862336   \n", "3      2011-01-03  18.031555  18.250118  17.963253  10.171988  10014663   \n", "4      2011-01-03   8.422694   8.629907   8.422694   6.294305   3652542   \n", "...           ...        ...        ...        ...        ...       ...   \n", "29775  2022-12-29  24.990000  25.049999  24.430000  18.930288  57901800   \n", "29776  2022-12-29  37.250000  37.480000  36.619999  37.150330   7890300   \n", "29777  2022-12-29  54.300869  55.776001  52.626392  51.326015   8786791   \n", "29778  2022-12-29  89.000000  89.949997  88.830002  80.014046  40110800   \n", "29779  2022-12-29  38.840000  38.990002  38.250000  37.525452   6444500   \n", "\n", "            tic  day  \n", "0      ABEV3.SA    0  \n", "1      BBAS3.SA    0  \n", "2      BBDC4.SA    0  \n", "3      ITUB4.SA    0  \n", "4      LREN3.SA    0  \n", "...         ...  ...  \n", "29775  PETR4.SA    3  \n", "29776  PRIO3.SA    3  \n", "29777  RENT3.SA    3  \n", "29778  VALE3.SA    3  \n", "29779  WEGE3.SA    3  \n", "\n", "[29780 rows x 8 columns]"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["print(len(TOP_BRL))\n", "\n", "portfolio_raw_df = YahooDownloader(start_date = '2011-01-01',\n", "                                end_date = '2022-12-31',\n", "                                ticker_list = TOP_BRL).fetch_data()\n", "portfolio_raw_df"]}, {"cell_type": "code", "execution_count": 47, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 444}, "id": "2UqpIXsuSKfO", "outputId": "436605d5-bc9e-4038-e3d7-7bdf140033d8"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>day</th>\n", "    </tr>\n", "    <tr>\n", "      <th>tic</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ABEV3.SA</th>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BBAS3.SA</th>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>BBDC4.SA</th>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ITUB4.SA</th>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LREN3.SA</th>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PETR4.SA</th>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>PRIO3.SA</th>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>RENT3.SA</th>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>VALE3.SA</th>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>WEGE3.SA</th>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "      <td>2978</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          date  open  high   low  close  volume   day\n", "tic                                                  \n", "ABEV3.SA  2978  2978  2978  2978   2978    2978  2978\n", "BBAS3.SA  2978  2978  2978  2978   2978    2978  2978\n", "BBDC4.SA  2978  2978  2978  2978   2978    2978  2978\n", "ITUB4.SA  2978  2978  2978  2978   2978    2978  2978\n", "LREN3.SA  2978  2978  2978  2978   2978    2978  2978\n", "PETR4.SA  2978  2978  2978  2978   2978    2978  2978\n", "PRIO3.SA  2978  2978  2978  2978   2978    2978  2978\n", "RENT3.SA  2978  2978  2978  2978   2978    2978  2978\n", "VALE3.SA  2978  2978  2978  2978   2978    2978  2978\n", "WEGE3.SA  2978  2978  2978  2978   2978    2978  2978"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["portfolio_raw_df.groupby(\"tic\").count()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Normalize Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We normalize the data dividing the time series of each stock by its maximum value, so that the dataframe contains values between 0 and 1."]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/Documentos/FinRL/finrl/meta/preprocessor/preprocessors.py:102: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '[0.00200262 0.00114137 0.0010422  ... 0.04565734 0.05214546 0.07760399]' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  X.loc[select_mask, self.columns] = self.scalers[value].transform(\n", "/home/<USER>/Documentos/FinRL/finrl/meta/preprocessor/preprocessors.py:102: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value '[0.   0.25 0.5  ... 0.25 0.5  0.75]' has dtype incompatible with int32, please explicitly cast to a compatible dtype first.\n", "  X.loc[select_mask, self.columns] = self.scalers[value].transform(\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>tic</th>\n", "      <th>day</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2011-01-03</td>\n", "      <td>0.353928</td>\n", "      <td>0.355383</td>\n", "      <td>0.359147</td>\n", "      <td>0.272027</td>\n", "      <td>0.002003</td>\n", "      <td>ABEV3.SA</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2011-01-03</td>\n", "      <td>0.566547</td>\n", "      <td>0.568771</td>\n", "      <td>0.572419</td>\n", "      <td>0.157351</td>\n", "      <td>0.014581</td>\n", "      <td>BBAS3.SA</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2011-01-03</td>\n", "      <td>0.396939</td>\n", "      <td>0.397479</td>\n", "      <td>0.399922</td>\n", "      <td>0.287841</td>\n", "      <td>0.021760</td>\n", "      <td>BBDC4.SA</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2011-01-03</td>\n", "      <td>0.466293</td>\n", "      <td>0.458661</td>\n", "      <td>0.467793</td>\n", "      <td>0.322621</td>\n", "      <td>0.062331</td>\n", "      <td>ITUB4.SA</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2011-01-03</td>\n", "      <td>0.153498</td>\n", "      <td>0.157120</td>\n", "      <td>0.155466</td>\n", "      <td>0.127001</td>\n", "      <td>0.072591</td>\n", "      <td>LREN3.SA</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29775</th>\n", "      <td>2022-12-29</td>\n", "      <td>0.663745</td>\n", "      <td>0.652514</td>\n", "      <td>0.662958</td>\n", "      <td>0.694899</td>\n", "      <td>0.082841</td>\n", "      <td>PETR4.SA</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29776</th>\n", "      <td>2022-12-29</td>\n", "      <td>0.858295</td>\n", "      <td>0.844144</td>\n", "      <td>0.878173</td>\n", "      <td>0.857373</td>\n", "      <td>0.048724</td>\n", "      <td>PRIO3.SA</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29777</th>\n", "      <td>2022-12-29</td>\n", "      <td>0.737949</td>\n", "      <td>0.754746</td>\n", "      <td>0.727218</td>\n", "      <td>0.728887</td>\n", "      <td>0.097191</td>\n", "      <td>RENT3.SA</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29778</th>\n", "      <td>2022-12-29</td>\n", "      <td>0.742905</td>\n", "      <td>0.746783</td>\n", "      <td>0.770759</td>\n", "      <td>0.916710</td>\n", "      <td>0.218546</td>\n", "      <td>VALE3.SA</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29779</th>\n", "      <td>2022-12-29</td>\n", "      <td>0.835988</td>\n", "      <td>0.830812</td>\n", "      <td>0.853795</td>\n", "      <td>0.859932</td>\n", "      <td>0.047379</td>\n", "      <td>WEGE3.SA</td>\n", "      <td>0.75</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>29780 rows × 8 columns</p>\n", "</div>"], "text/plain": ["             date      open      high       low     close    volume       tic  \\\n", "0      2011-01-03  0.353928  0.355383  0.359147  0.272027  0.002003  ABEV3.SA   \n", "1      2011-01-03  0.566547  0.568771  0.572419  0.157351  0.014581  BBAS3.SA   \n", "2      2011-01-03  0.396939  0.397479  0.399922  0.287841  0.021760  BBDC4.SA   \n", "3      2011-01-03  0.466293  0.458661  0.467793  0.322621  0.062331  ITUB4.SA   \n", "4      2011-01-03  0.153498  0.157120  0.155466  0.127001  0.072591  LREN3.SA   \n", "...           ...       ...       ...       ...       ...       ...       ...   \n", "29775  2022-12-29  0.663745  0.652514  0.662958  0.694899  0.082841  PETR4.SA   \n", "29776  2022-12-29  0.858295  0.844144  0.878173  0.857373  0.048724  PRIO3.SA   \n", "29777  2022-12-29  0.737949  0.754746  0.727218  0.728887  0.097191  RENT3.SA   \n", "29778  2022-12-29  0.742905  0.746783  0.770759  0.916710  0.218546  VALE3.SA   \n", "29779  2022-12-29  0.835988  0.830812  0.853795  0.859932  0.047379  WEGE3.SA   \n", "\n", "        day  \n", "0      0.00  \n", "1      0.00  \n", "2      0.00  \n", "3      0.00  \n", "4      0.00  \n", "...     ...  \n", "29775  0.75  \n", "29776  0.75  \n", "29777  0.75  \n", "29778  0.75  \n", "29779  0.75  \n", "\n", "[29780 rows x 8 columns]"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["portfolio_norm_df = GroupByScaler(by=\"tic\", scaler=MaxAbsScaler).fit_transform(portfolio_raw_df)\n", "portfolio_norm_df"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["df_portfolio = portfolio_norm_df[[\"date\", \"tic\", \"close\", \"high\", \"low\"]]\n", "\n", "df_portfolio_train = df_portfolio[(df_portfolio[\"date\"] >= \"2011-01-01\") & (df_portfolio[\"date\"] < \"2019-12-31\")]\n", "df_portfolio_2020 = df_portfolio[(df_portfolio[\"date\"] >= \"2020-01-01\") & (df_portfolio[\"date\"] < \"2020-12-31\")]\n", "df_portfolio_2021 = df_portfolio[(df_portfolio[\"date\"] >= \"2021-01-01\") & (df_portfolio[\"date\"] < \"2021-12-31\")]\n", "df_portfolio_2022 = df_portfolio[(df_portfolio[\"date\"] >= \"2022-01-01\") & (df_portfolio[\"date\"] < \"2022-12-31\")]"]}, {"cell_type": "markdown", "metadata": {"id": "pM829994GWo3"}, "source": ["### Instantiate Environment\n", "\n", "Using the `PortfolioOptimizationEnv`, it's easy to instantiate a portfolio optimization environment for reinforcement learning agents. In the example below, we use the dataframe created before to start an environment."]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["environment = PortfolioOptimizationEnv(\n", "        df_portfolio_train,\n", "        initial_amount=100000,\n", "        comission_fee_pct=0.0025,\n", "        time_window=50,\n", "        features=[\"close\", \"high\", \"low\"],\n", "        normalize_df=None\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Instantiate Model\n", "\n", "Now, we can instantiate the model using FinRL API. In this example, we are going to use the EIIE architecture introduced by <PERSON> et. al.\n", "\n", ":exclamation: **Note:** Remember to set the architecture's `time_window` parameter with the same value of the environment's `time_window`."]}, {"cell_type": "code", "execution_count": 51, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["750b2ea28d2a439db3fc5034927dbce2", "c172e120fc5e4f9ab13bf8599d868b5f", "4b2aa7128c5d4d15bb794eb76faccd6a", "317393fb13c0449abfff29a4949553a0", "8cb75a82e5374c51b1f47a6e15783177", "9cb3d937be5d4f7cac192b392218ef37", "b27b9cc333ac44a5bb2cec60d02f16c0", "6a1187acb99d44c68e27cd5aad879ff1", "6a5c9dbaddc441d390d4827c170cbe9c", "1f84695a1caf4c80b29eb5eea90bb29a", "a7a6884bfdb642b9b342f7cda49d7d67"]}, "id": "wr82W3E0uQSo", "outputId": "61dcf1f5-7cf0-40b2-85bd-3f7dd943ddc6", "scrolled": true}, "outputs": [], "source": ["# set PolicyGradient parameters\n", "model_kwargs = {\n", "    \"lr\": 0.01,\n", "    \"policy\": EIIE,\n", "}\n", "\n", "# here, we can set EIIE's parameters\n", "policy_kwargs = {\n", "    \"k_size\": 3,\n", "    \"time_window\": 50,\n", "}\n", "\n", "model = DRLAgent(environment).get_model(\"pg\", device, model_kwargs, policy_kwargs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Train Model"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/40 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 385040.96875\n", "Final accumulative portfolio value: 3.8504096875\n", "Maximum DrawDown: -0.43694138095630164\n", "Sharpe ratio: 0.8325641434157057\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  2%|▎         | 1/40 [00:22<14:27, 22.23s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 432366.90625\n", "Final accumulative portfolio value: 4.3236690625\n", "Maximum DrawDown: -0.440061257034582\n", "Sharpe ratio: 0.9433159229705296\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  5%|▌         | 2/40 [00:44<14:00, 22.11s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 520977.8125\n", "Final accumulative portfolio value: 5.209778125\n", "Maximum DrawDown: -0.4695879959202859\n", "Sharpe ratio: 0.9829064434985563\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  8%|▊         | 3/40 [01:06<13:41, 22.19s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 603017.0625\n", "Final accumulative portfolio value: 6.030170625\n", "Maximum DrawDown: -0.5303969892231588\n", "Sharpe ratio: 0.9678411790552034\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 10%|█         | 4/40 [01:28<13:17, 22.16s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 691438.375\n", "Final accumulative portfolio value: 6.91438375\n", "Maximum DrawDown: -0.5345244999288666\n", "Sharpe ratio: 1.0317940509881482\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 12%|█▎        | 5/40 [01:50<12:52, 22.07s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 823173.625\n", "Final accumulative portfolio value: 8.23173625\n", "Maximum DrawDown: -0.5652736234992821\n", "Sharpe ratio: 1.035537312266153\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 15%|█▌        | 6/40 [02:13<12:38, 22.32s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 945736.9375\n", "Final accumulative portfolio value: 9.457369375\n", "Maximum DrawDown: -0.5914935086035373\n", "Sharpe ratio: 1.0374710239521467\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 18%|█▊        | 7/40 [02:35<12:13, 22.24s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 969559.1875\n", "Final accumulative portfolio value: 9.695591875\n", "Maximum DrawDown: -0.6471614235017352\n", "Sharpe ratio: 0.9770227278765214\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 20%|██        | 8/40 [02:57<11:46, 22.09s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1104534.5\n", "Final accumulative portfolio value: 11.045345\n", "Maximum DrawDown: -0.6301651263519266\n", "Sharpe ratio: 1.0237892697969757\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 22%|██▎       | 9/40 [03:20<11:38, 22.54s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1119599.0\n", "Final accumulative portfolio value: 11.19599\n", "Maximum DrawDown: -0.6684622043532014\n", "Sharpe ratio: 0.9801229201409899\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 25%|██▌       | 10/40 [03:43<11:19, 22.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1268040.125\n", "Final accumulative portfolio value: 12.68040125\n", "Maximum DrawDown: -0.6547845447746306\n", "Sharpe ratio: 1.0172949402189784\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 28%|██▊       | 11/40 [04:06<10:59, 22.74s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1231757.125\n", "Final accumulative portfolio value: 12.31757125\n", "Maximum DrawDown: -0.6960534485362004\n", "Sharpe ratio: 0.9604649744547805\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 30%|███       | 12/40 [04:28<10:30, 22.52s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1405231.875\n", "Final accumulative portfolio value: 14.05231875\n", "Maximum DrawDown: -0.6633583007958319\n", "Sharpe ratio: 1.0233517470008788\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 32%|███▎      | 13/40 [04:50<10:06, 22.48s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1362994.625\n", "Final accumulative portfolio value: 13.62994625\n", "Maximum DrawDown: -0.7045986884989512\n", "Sharpe ratio: 0.9665800237678901\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 35%|███▌      | 14/40 [05:13<09:42, 22.40s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1459606.75\n", "Final accumulative portfolio value: 14.5960675\n", "Maximum DrawDown: -0.690705709307877\n", "Sharpe ratio: 0.9914607276189837\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 38%|███▊      | 15/40 [05:35<09:16, 22.26s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1490526.0\n", "Final accumulative portfolio value: 14.90526\n", "Maximum DrawDown: -0.6944851332843318\n", "Sharpe ratio: 0.9812963829657945\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 40%|████      | 16/40 [05:57<08:55, 22.32s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1549355.375\n", "Final accumulative portfolio value: 15.49355375\n", "Maximum DrawDown: -0.6933624028469978\n", "Sharpe ratio: 0.9863412896316986\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 42%|████▎     | 17/40 [06:20<08:34, 22.35s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1606115.75\n", "Final accumulative portfolio value: 16.0611575\n", "Maximum DrawDown: -0.69369766089119\n", "Sharpe ratio: 0.9849120153612915\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 45%|████▌     | 18/40 [06:43<08:16, 22.57s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1697176.5\n", "Final accumulative portfolio value: 16.971765\n", "Maximum DrawDown: -0.6837630642661545\n", "Sharpe ratio: 0.9983994393963533\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 48%|████▊     | 19/40 [07:05<07:55, 22.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1757279.375\n", "Final accumulative portfolio value: 17.57279375\n", "Maximum DrawDown: -0.6909155474653719\n", "Sharpe ratio: 0.994707027829647\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 50%|█████     | 20/40 [07:28<07:32, 22.60s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1830498.75\n", "Final accumulative portfolio value: 18.3049875\n", "Maximum DrawDown: -0.6930789420297205\n", "Sharpe ratio: 0.996955913943652\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 52%|█████▎    | 21/40 [07:51<07:10, 22.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1871996.125\n", "Final accumulative portfolio value: 18.71996125\n", "Maximum DrawDown: -0.7041261221075639\n", "Sharpe ratio: 0.9863575412121732\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 55%|█████▌    | 22/40 [08:13<06:48, 22.67s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1915910.25\n", "Final accumulative portfolio value: 19.1591025\n", "Maximum DrawDown: -0.7099325656210782\n", "Sharpe ratio: 0.9814408191361197\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 57%|█████▊    | 23/40 [08:36<06:24, 22.61s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1884170.375\n", "Final accumulative portfolio value: 18.84170375\n", "Maximum DrawDown: -0.7317997718333524\n", "Sharpe ratio: 0.9543341181855992\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 60%|██████    | 24/40 [08:58<05:58, 22.40s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1978879.625\n", "Final accumulative portfolio value: 19.78879625\n", "Maximum DrawDown: -0.7194680985917248\n", "Sharpe ratio: 0.9743714326713419\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 62%|██████▎   | 25/40 [09:21<05:38, 22.55s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 2154036.75\n", "Final accumulative portfolio value: 21.5403675\n", "Maximum DrawDown: -0.6659672023127168\n", "Sharpe ratio: 1.0360744568254021\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 65%|██████▌   | 26/40 [09:43<05:14, 22.46s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1898927.25\n", "Final accumulative portfolio value: 18.9892725\n", "Maximum DrawDown: -0.7793056952332392\n", "Sharpe ratio: 0.9144819413600235\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 68%|██████▊   | 27/40 [10:06<04:53, 22.56s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1993799.0\n", "Final accumulative portfolio value: 19.93799\n", "Maximum DrawDown: -0.7006401932993662\n", "Sharpe ratio: 1.0057436299325901\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 70%|███████   | 28/40 [10:28<04:28, 22.39s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 2330999.25\n", "Final accumulative portfolio value: 23.3099925\n", "Maximum DrawDown: -0.6696759833063628\n", "Sharpe ratio: 1.0477967195638818\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 72%|███████▎  | 29/40 [10:50<04:07, 22.50s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1937146.0\n", "Final accumulative portfolio value: 19.37146\n", "Maximum DrawDown: -0.7936668482286839\n", "Sharpe ratio: 0.9054145210236558\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 75%|███████▌  | 30/40 [11:13<03:44, 22.47s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1709073.0\n", "Final accumulative portfolio value: 17.09073\n", "Maximum DrawDown: -0.7492562302933228\n", "Sharpe ratio: 0.9294165846919152\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 78%|███████▊  | 31/40 [11:35<03:21, 22.42s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1988512.625\n", "Final accumulative portfolio value: 19.88512625\n", "Maximum DrawDown: -0.6967059065829422\n", "Sharpe ratio: 1.0133491769379754\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 80%|████████  | 32/40 [11:57<02:58, 22.25s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 2280850.0\n", "Final accumulative portfolio value: 22.8085\n", "Maximum DrawDown: -0.6308013843352027\n", "Sharpe ratio: 1.0827259449091051\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 82%|████████▎ | 33/40 [12:20<02:37, 22.49s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 2535594.75\n", "Final accumulative portfolio value: 25.3559475\n", "Maximum DrawDown: -0.7054483480941727\n", "Sharpe ratio: 1.0358760032435206\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 85%|████████▌ | 34/40 [12:43<02:15, 22.52s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1853363.125\n", "Final accumulative portfolio value: 18.53363125\n", "Maximum DrawDown: -0.80204361278234\n", "Sharpe ratio: 0.8861850629786099\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 88%|████████▊ | 35/40 [13:05<01:52, 22.42s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1976948.875\n", "Final accumulative portfolio value: 19.76948875\n", "Maximum DrawDown: -0.7012713940750688\n", "Sharpe ratio: 1.0144723339300001\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 90%|█████████ | 36/40 [13:28<01:30, 22.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1496496.375\n", "Final accumulative portfolio value: 14.96496375\n", "Maximum DrawDown: -0.598138804949116\n", "Sharpe ratio: 1.1625482738032367\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 92%|█████████▎| 37/40 [13:50<01:07, 22.54s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1573181.25\n", "Final accumulative portfolio value: 15.7318125\n", "Maximum DrawDown: -0.5894304176343221\n", "Sharpe ratio: 1.1632205152623596\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 95%|█████████▌| 38/40 [14:12<00:44, 22.43s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1642286.0\n", "Final accumulative portfolio value: 16.42286\n", "Maximum DrawDown: -0.5931878962991857\n", "Sharpe ratio: 1.1784491119243807\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 98%|█████████▊| 39/40 [14:35<00:22, 22.55s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 1716964.375\n", "Final accumulative portfolio value: 17.16964375\n", "Maximum DrawDown: -0.5883583583593176\n", "Sharpe ratio: 1.1939969246876079\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 40/40 [14:58<00:00, 22.46s/it]\n"]}, {"data": {"text/plain": ["<finrl.agents.portfolio_optimization.algorithms.PolicyGradient at 0x7fd2cdc03eb0>"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["DRLAgent.train_model(model, episodes=40)"]}, {"cell_type": "markdown", "metadata": {"id": "JE7X3qEeXOr4"}, "source": ["### Save Model"]}, {"cell_type": "code", "execution_count": 53, "metadata": {"id": "YcWuPgPvXNpP"}, "outputs": [], "source": ["torch.save(model.train_policy.state_dict(), \"policy_EIIE.pt\")"]}, {"cell_type": "markdown", "metadata": {"id": "7FRK9A98XVck"}, "source": ["## Test Model"]}, {"cell_type": "markdown", "metadata": {"id": "IFYB9iGwAPSh"}, "source": ["### Instantiate different environments\n", "\n", "Since we have three different periods of time, we need three different environments instantiated to simulate them."]}, {"cell_type": "code", "execution_count": 54, "metadata": {"id": "HhsL5Cxx9d5s"}, "outputs": [], "source": ["environment_2020 = PortfolioOptimizationEnv(\n", "    df_portfolio_2020,\n", "    initial_amount=100000,\n", "    comission_fee_pct=0.0025,\n", "    time_window=50,\n", "    features=[\"close\", \"high\", \"low\"],\n", "    normalize_df=None\n", ")\n", "\n", "environment_2021 = PortfolioOptimizationEnv(\n", "    df_portfolio_2021,\n", "    initial_amount=100000,\n", "    comission_fee_pct=0.0025,\n", "    time_window=50,\n", "    features=[\"close\", \"high\", \"low\"],\n", "    normalize_df=None\n", ")\n", "\n", "environment_2022 = PortfolioOptimizationEnv(\n", "    df_portfolio_2022,\n", "    initial_amount=100000,\n", "    comission_fee_pct=0.0025,\n", "    time_window=50,\n", "    features=[\"close\", \"high\", \"low\"],\n", "    normalize_df=None\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "Y4RuS2pRAa4H"}, "source": ["### Test EIIE architecture\n", "Now, we can test the EIIE architecture in the three different test periods. It's important no note that, in this code, we load the saved policy even though it's not necessary just to show how to save and load your model."]}, {"cell_type": "code", "execution_count": 55, "metadata": {"id": "JeRy__TI9CAs"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 300425.875\n", "Final accumulative portfolio value: 3.00425875\n", "Maximum DrawDown: -0.44018999999999997\n", "Sharpe ratio: 1.9037659723061418\n", "=================================\n", "=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 135694.625\n", "Final accumulative portfolio value: 1.35694625\n", "Maximum DrawDown: -0.1553491952446484\n", "Sharpe ratio: 1.412587489380964\n", "=================================\n", "=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 101662.1484375\n", "Final accumulative portfolio value: 1.016621484375\n", "Maximum DrawDown: -0.2406645708757662\n", "Sharpe ratio: 0.2027036585970349\n", "=================================\n"]}], "source": ["EIIE_results = {\n", "    \"training\": environment._asset_memory[\"final\"],\n", "    \"2020\": {},\n", "    \"2021\": {},\n", "    \"2022\": {}\n", "}\n", "\n", "# instantiate an architecture with the same arguments used in training\n", "# and load with load_state_dict.\n", "policy = EIIE(time_window=50, device=device)\n", "policy.load_state_dict(torch.load(\"policy_EIIE.pt\"))\n", "\n", "# 2020\n", "DRLAgent.DRL_validation(model, environment_2020, policy=policy)\n", "EIIE_results[\"2020\"][\"value\"] = environment_2020._asset_memory[\"final\"]\n", "\n", "# 2021\n", "DRLAgent.DRL_validation(model, environment_2021, policy=policy)\n", "EIIE_results[\"2021\"][\"value\"] = environment_2021._asset_memory[\"final\"]\n", "\n", "# 2022\n", "DRLAgent.DRL_validation(model, environment_2022, policy=policy)\n", "EIIE_results[\"2022\"][\"value\"] = environment_2022._asset_memory[\"final\"]"]}, {"cell_type": "markdown", "metadata": {"id": "LZc5PpbaBU-J"}, "source": ["### Test Uniform Buy and Hold\n", "For comparison, we will also test the performance of a uniform buy and hold strategy. In this strategy, the portfolio has no remaining cash and the same percentage of money is allocated in each asset."]}, {"cell_type": "code", "execution_count": 56, "metadata": {"id": "ntHO_UIs-83T"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 422020.28125\n", "Final accumulative portfolio value: 4.2202028125\n", "Maximum DrawDown: -0.47191689613863286\n", "Sharpe ratio: 0.8074281745795617\n", "=================================\n", "=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 171398.5\n", "Final accumulative portfolio value: 1.713985\n", "Maximum DrawDown: -0.250788671875\n", "Sharpe ratio: 1.7169357267189664\n", "=================================\n", "=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 96248.625\n", "Final accumulative portfolio value: 0.96248625\n", "Maximum DrawDown: -0.1693852421222789\n", "Sharpe ratio: -0.12241539200411489\n", "=================================\n", "=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 113848.515625\n", "Final accumulative portfolio value: 1.13848515625\n", "Maximum DrawDown: -0.16645801969650909\n", "Sharpe ratio: 0.8294354891818461\n", "=================================\n"]}], "source": ["UBAH_results = {\n", "    \"train\": {},\n", "    \"2020\": {},\n", "    \"2021\": {},\n", "    \"2022\": {}\n", "}\n", "\n", "PORTFOLIO_SIZE = len(TOP_BRL)\n", "\n", "# train period\n", "terminated = False\n", "environment.reset()\n", "while not terminated:\n", "    action = [0] + [1/PORTFOLIO_SIZE] * PORTFOLIO_SIZE\n", "    _, _, terminated, _ = environment.step(action)\n", "UBAH_results[\"train\"][\"value\"] = environment._asset_memory[\"final\"]\n", "\n", "# 2020\n", "terminated = False\n", "environment_2020.reset()\n", "while not terminated:\n", "    action = [0] + [1/PORTFOLIO_SIZE] * PORTFOLIO_SIZE\n", "    _, _, terminated, _ = environment_2020.step(action)\n", "UBAH_results[\"2020\"][\"value\"] = environment_2020._asset_memory[\"final\"]\n", "\n", "# 2021\n", "terminated = False\n", "environment_2021.reset()\n", "while not terminated:\n", "    action = [0] + [1/PORTFOLIO_SIZE] * PORTFOLIO_SIZE\n", "    _, _, terminated, _ = environment_2021.step(action)\n", "UBAH_results[\"2021\"][\"value\"] = environment_2021._asset_memory[\"final\"]\n", "\n", "# 2022\n", "terminated = False\n", "environment_2022.reset()\n", "while not terminated:\n", "    action = [0] + [1/PORTFOLIO_SIZE] * PORTFOLIO_SIZE\n", "    _, _, terminated, _ = environment_2022.step(action)\n", "UBAH_results[\"2022\"][\"value\"] = environment_2022._asset_memory[\"final\"]"]}, {"cell_type": "markdown", "metadata": {"id": "kBMM7hAHC6rq"}, "source": ["### Plot graphics"]}, {"cell_type": "code", "execution_count": 57, "metadata": {"id": "n8YrDNpeC71w"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "%matplotlib inline \n", "\n", "plt.plot(UBAH_results[\"train\"][\"value\"], label=\"Buy and Hold\")\n", "plt.plot(EIIE_results[\"training\"], label=\"EIIE\")\n", "\n", "plt.xlabel(\"Days\")\n", "plt.ylabel(\"Portfolio Value\")\n", "plt.title(\"Performance in training period\")\n", "plt.legend()\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 58, "metadata": {"id": "dQniascoDIH2"}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(UBAH_results[\"2020\"][\"value\"], label=\"Buy and Hold\")\n", "plt.plot(EIIE_results[\"2020\"][\"value\"], label=\"EIIE\")\n", "\n", "plt.xlabel(\"Days\")\n", "plt.ylabel(\"Portfolio Value\")\n", "plt.title(\"Performance in 2020\")\n", "plt.legend()\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 59, "metadata": {"id": "1hJtnW7QDIt2"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(UBAH_results[\"2021\"][\"value\"], label=\"Buy and Hold\")\n", "plt.plot(EIIE_results[\"2021\"][\"value\"], label=\"EIIE\")\n", "\n", "plt.xlabel(\"Days\")\n", "plt.ylabel(\"Portfolio Value\")\n", "plt.title(\"Performance in 2021\")\n", "plt.legend()\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 60, "metadata": {"id": "1hJD79w-DJ<PERSON><PERSON>"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(UBAH_results[\"2022\"][\"value\"], label=\"Buy and Hold\")\n", "plt.plot(EIIE_results[\"2022\"][\"value\"], label=\"EIIE\")\n", "\n", "plt.xlabel(\"Days\")\n", "plt.ylabel(\"Portfolio Value\")\n", "plt.title(\"Performance in 2022\")\n", "plt.legend()\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that the agent is able to learn a good policy but its performance is worse the more the test period advances into the future. To get a better performance in 2022, for example, the agent should probably be trained again using more recent data."]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"1f84695a1caf4c80b29eb5eea90bb29a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "317393fb13c0449abfff29a4949553a0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1f84695a1caf4c80b29eb5eea90bb29a", "placeholder": "​", "style": "IPY_MODEL_a7a6884bfdb642b9b342f7cda49d7d67", "value": " 10/250 [05:53&lt;2:10:07, 32.53s/it]"}}, "4b2aa7128c5d4d15bb794eb76faccd6a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6a1187acb99d44c68e27cd5aad879ff1", "max": 250, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6a5c9dbaddc441d390d4827c170cbe9c", "value": 10}}, "6a1187acb99d44c68e27cd5aad879ff1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6a5c9dbaddc441d390d4827c170cbe9c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "750b2ea28d2a439db3fc5034927dbce2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c172e120fc5e4f9ab13bf8599d868b5f", "IPY_MODEL_4b2aa7128c5d4d15bb794eb76faccd6a", "IPY_MODEL_317393fb13c0449abfff29a4949553a0"], "layout": "IPY_MODEL_8cb75a82e5374c51b1f47a6e15783177"}}, "8cb75a82e5374c51b1f47a6e15783177": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9cb3d937be5d4f7cac192b392218ef37": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a7a6884bfdb642b9b342f7cda49d7d67": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b27b9cc333ac44a5bb2cec60d02f16c0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c172e120fc5e4f9ab13bf8599d868b5f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9cb3d937be5d4f7cac192b392218ef37", "placeholder": "​", "style": "IPY_MODEL_b27b9cc333ac44a5bb2cec60d02f16c0", "value": "  4%"}}}}}, "nbformat": 4, "nbformat_minor": 1}