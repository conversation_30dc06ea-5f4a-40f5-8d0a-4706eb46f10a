# External Sources

The following contents are collected and referred by AI4Finance community during the development of FinRL and related projects. Some of them are educational and relatively easy while some others are professional and need advanced knowledge. We appreciate and respect the effort of all these contents' authors and developers.

## Proof-of-concept

[1] [FinRL: Deep Reinforcement Learning Framework to Automate Trading in Quantitative Finance](https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3955949) Deep reinforcement learning framework to automate trading in quantitative finance, ACM International Conference on AI in Finance, ICAIF 2021.

[2] [FinRL: A Deep Reinforcement Learning Library for Automated Stock Trading in Quantitative Finance](https://arxiv.org/abs/2011.09607) A deep reinforcement learning library for automated stock trading in quantitative finance, Deep RL Workshop, NeurIPS 2020.

[3] [Practical deep reinforcement learning approach for stock trading](https://arxiv.org/abs/1811.07522). NeurIPS Workshop on Challenges and Opportunities for AI in Financial Services: the Impact of Fairness, Explainability, Accuracy, and Privacy, 2018.

[4] [Deep Reinforcement Learning for Trading](https://arxiv.org/abs/1911.10107). <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. The Journal of Financial Data Science 2, no. 2 (2020): 25-40.

[5] [A Deep Reinforcement Learning Framework for the Financial Portfolio Management Problem](https://arxiv.org/abs/1706.10059). Jiang, Zhengyao, Dixing Xu, and Jinjun Liang. arXiv preprint arXiv:1706.10059 (2017).

## DRL Algorithms/Libraries

[1] [Documentation of ElegentRL](https://elegantrl.readthedocs.io) by AI4Finance Foundation.

[2] [Spinning Up in Deep RL](https://spinningup.openai.com/) by OpenAI.

## Theory

[1] [Deep Reinforcement Learning: An Overview](https://arxiv.org/abs/1701.07274) Li, Yuxi. arXiv preprint arXiv:1701.07274 (2017).

[2] Continuous‐time mean–variance portfolio selection: A reinforcement learning framework. Mathematical Finance, 30(4), pp.1273-1308. Wang, H. and Zhou, X.Y., 2020.

[3] Mao Guan and Xiao-Yang Liu. Explainable deep reinforcement learning for portfolio man- agement: An empirical approach. ACM International Conference on AI in Finance, ICAIF 2021.

[4] [ICAIF](https://ai-finance.org) International Conference on AI in Finance.

## Trading Strategies

[1] [Deep reinforcement learning for automated stock trading: an ensemble strategy](https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3690996). ACM International Conference on AI in Finance, 2020.

[2] [FinRL-Podracer](https://arxiv.org/abs/2111.05188): High performance and scalable deep reinforcement learning for quantitative finance. ACM International Conference on AI in Finance, ICAIF 2021.

[3] Multi-agent reinforcement learning for liquidation strategy analysis, [paper](https://arxiv.org/abs/1906.11046) and [codes](https://github.com/WenhangBao/Multi-Agent-RL-for-Liquidation). Workshop on Applications and Infrastructure for Multi-Agent Learning, ICML 2019.

[4] [Risk-Sensitive Reinforcement Learning: a Martingale Approach to Reward Uncertainty.](https://arxiv.org/abs/2006.12686) International Conference on AI in Finance, ICAIF 2020.

[5] [Cryptocurrency Trading Using Machine Learning](https://www.mdpi.com/1911-8074/13/8/178). Journal of Risk and Financial Management, August 2020.

[6] [Multi-Agent Reinforcement Learning in a Realistic Limit Order Book Market Simulation](https://arxiv.org/abs/2006.05574). Michaël Karpe, Jin Fang, Zhongyao Ma, Chen Wang. International Conference on AI in Finance (ICAIF’20), September 2020.

[7] [Market Making via Reinforcement Learning](https://arxiv.org/abs/1804.04216). Thomas Spooner, John Fearnley, Rahul Savani, Andreas Koukorinis. AAMAS2018 Conference Proceedings

[8] [Financial Trading as a Game: A Deep Reinforcement Learning Approach](https://arxiv.org/abs/1807.02787) Huang, Chien Yi. arXiv preprint arXiv:1807.02787 (2018).

[9] [Deep Hedging: Hedging Derivatives Under Generic Market Frictions Using Reinforcement Learning](https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3355706) Buehler, Hans, Lukas Gonon, Josef Teichmann, Ben Wood, Baranidharan Mohan, and Jonathan Kochems. Swiss Finance Institute Research Paper 19-80 (2019).

## Financial Big Data

[1] [FinRL-Meta](https://arxiv.org/abs/2112.06753): A Universe of Near-Real Market Environments for Data-Driven Deep Reinforcement Learning in Quantitative Finance. NeurIPS 2021 Data-Centric AI Workshop

## Interpretation and Explainability

[1] [Explainable Deep Reinforcement Learning for Portfolio Management: An Empirical Approach](https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3958005;). Guan, M. and Liu, X.Y.. ACM International Conference on AI in Finance, 2021.

## Tools or Softwares

[1] [FinRL](https://github.com/AI4Finance-Foundation/FinRL) by AI4Finance Foundation.

[2] [FinRL-Meta](https://github.com/AI4Finance-Foundation/FinRL-Meta): A Universe of Near-Real Market Environments for Data-Driven Deep Reinforcement Learning in Quantitative Finance, by AI4Finance Foundation.

[3] [ElegantRL](https://github.com/AI4Finance-Foundation/ElegantRL): a DRL library developed by AI4Finance Foundation.

[4] [Stable-Baselines3](https://github.com/DLR-RM/stable-baselines3): Reliable Reinforcement Learning Implementations.

## Survey

[1] [Recent Advances in Reinforcement Learning in Finance](https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3971071). Hambly, B., Xu, R. and Yang, H., 2021.

[2] [Deep Reinforcement Learning for Trading—A Critical Survey](https://www.mdpi.com/2306-5729/6/11/119). Adrian Millea, 2021.

[3] [Modern Perspectives on Reinforcement Learning in Finance](https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3449401) Kolm, Petter N. and Ritter, Gordon. The Journal of Machine Learning in Finance, Vol. 1, No. 1, 2020.

[4] [Reinforcement Learning in Economics and Finance](https://arxiv.org/abs/2003.10014) Charpentier, Arthur, Romuald Elie, and Carl Remlinger.  Computational Economics (2021): 1-38.

[5] [Comprehensive Review of Deep Reinforcement Learning Methods and Applications in Economics](https://www.mdpi.com/2227-7390/8/10/1640) Mosavi, Amirhosein, Yaser Faghan, Pedram Ghamisi, Puhong Duan, Sina Faizollahzadeh Ardabili, Ely Salwana, and Shahab S. Band. Mathematics 8, no. 10 (2020): 1640.

## Education

[1] [Coursera Overview of Advanced Methods of Reinforcement Learning in Finance](https://www.coursera.org/learn/advanced-methods-reinforcement-learning-finance). By Igor Halperin, at NYU.

[2] [*Foundations of reinforcement learning with applications in finance*](https://stanford.edu/~ashlearn/RLForFinanceBook/book.pdf) by Ashwin Rao, Tikhon Jelvis, Stanford University
