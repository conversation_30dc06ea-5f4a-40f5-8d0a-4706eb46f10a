:github_url: https://github.com/AI4Finance-Foundation/FinRL

2-Advance
========================

This section is recommended for users with some familiarity of FinRL or FinRL-Meta (or already run the notebooks in "1-Introduction").

Notebooks in this section includes:

1. `FinRL_Compare_ElegantRL_RLlib_Stablebaseline3.ipynb <https://github.com/AI4Finance-Foundation/FinRL/blob/master/tutorials/2-Advance/FinRL_Compare_ElegantRL_RLlib_Stablebaseline3.ipynb>`_

In this notebook, we compare the three DRL libraries that supported in FinRL. Users who know these DRL libraries might find this interesting.

2. `FinRL_Ensemble_StockTrading_ICAIF_2020.ipynb <https://github.com/AI4Finance-Foundation/FinRL-Tutorials/blob/master/2-Advance/FinRL_Ensemble_StockTrading_ICAIF_2020.ipynb>`_

In this notebook, we implement an "ensemble agent", which is a ensemble of several popular DRL algorithms. Then we compare the performance of the ensemble agent and other DRL agents on the portfolio allocation task.

`FinRL_PortfolioAllocation_Explainable_DRL.ipynb <https://github.com/AI4Finance-Foundation/FinRL-Tutorials/blob/master/2-Advance/FinRL_PortfolioAllocation_Explainable_DRL.ipynb>`_.
