{"cells": [{"cell_type": "markdown", "metadata": {"id": "v7Cycmf3Zbok"}, "source": ["# Stock NeurIPS2018 Part 3. Backtest\n", "This series is a reproduction of paper *the process in the paper Practical Deep Reinforcement Learning Approach for Stock Trading*. \n", "\n", "This is the third and last part of the NeurIPS2018 series, introducing how to use use the agents we trained to do backtest, and compare with baselines such as Mean Variance Optimization and DJIA index.\n", "\n", "Other demos can be found at the repo of [FinRL-Tutorials]((https://github.com/AI4Finance-Foundation/FinRL-Tutorials))."]}, {"cell_type": "markdown", "metadata": {"id": "1oWbj4HgqHBg"}, "source": ["# Part 1. Install Packages"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "QJgoEYx3p_NG"}, "outputs": [], "source": ["## install finrl library\n", "!pip install git+https://github.com/AI4Finance-Foundation/FinRL.git"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "mqfBOKz-qJYF"}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "from stable_baselines3 import A2C, DDPG, PPO, SAC, TD3\n", "\n", "from finrl.agents.stablebaselines3.models import DRLAgent\n", "from finrl.config import INDICATORS, TRAINED_MODEL_DIR\n", "from finrl.meta.env_stock_trading.env_stocktrading import StockTradingEnv\n", "from finrl.meta.preprocessor.yahoodownloader import YahooDownloader\n", "\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {"id": "mUF2P4hmqVjh"}, "source": ["# Part 2. Backtesting"]}, {"cell_type": "markdown", "metadata": {"id": "BdU6qLsVWDxI"}, "source": ["To backtest the agents, upload trade_data.csv in the same directory of this notebook. For Colab users, just upload trade_data.csv to the default directory."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "mSjBHn_MZr4U"}, "outputs": [], "source": ["train = pd.read_csv('train_data.csv')\n", "trade = pd.read_csv('trade_data.csv')\n", "\n", "# If you are not using the data generated from part 1 of this tutorial, make sure \n", "# it has the columns and index in the form that could be make into the environment. \n", "# Then you can comment and skip the following lines.\n", "train = train.set_index(train.columns[0])\n", "train.index.names = ['']\n", "trade = trade.set_index(trade.columns[0])\n", "trade.index.names = ['']"]}, {"cell_type": "markdown", "metadata": {"id": "qu4Ey54b36oL"}, "source": ["Then, upload the trained agent to the same directory, and set the corresponding variable to True."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "Z_mVZM4IIa55"}, "outputs": [], "source": ["if_using_a2c = True\n", "if_using_ddpg = True\n", "if_using_ppo = True\n", "if_using_td3 = True\n", "if_using_sac = True"]}, {"cell_type": "markdown", "metadata": {"id": "73D4oRqAIkYj"}, "source": ["Load the agents"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "6CagrX0I36ZN"}, "outputs": [], "source": ["trained_a2c = A2C.load(TRAINED_MODEL_DIR + \"/agent_a2c\") if if_using_a2c else None\n", "trained_ddpg = DDPG.load(TRAINED_MODEL_DIR + \"/agent_ddpg\") if if_using_ddpg else None\n", "trained_ppo = PPO.load(TRAINED_MODEL_DIR + \"/agent_ppo\") if if_using_ppo else None\n", "trained_td3 = TD3.load(TRAINED_MODEL_DIR + \"/agent_td3\") if if_using_td3 else None\n", "trained_sac = SAC.load(TRAINED_MODEL_DIR + \"/agent_sac\") if if_using_sac else None"]}, {"cell_type": "markdown", "metadata": {"id": "U5mmgQF_h1jQ"}, "source": ["### Trading (Out-of-sample Performance)\n", "\n", "We update periodically in order to take full advantage of the data, e.g., retrain quarterly, monthly or weekly. We also tune the parameters along the way, in this notebook we use the in-sample data from 2009-01 to 2020-07 to tune the parameters once, so there is some alpha decay here as the length of trade date extends. \n", "\n", "Numerous hyperparameters – e.g. the learning rate, the total number of samples to train on – influence the learning process and are usually determined by testing some variations."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4H_w3SaBAkKU", "outputId": "fdaed3a7-d3a9-4cde-d194-ee4576057175"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Stock Dimension: 29, State Space: 291\n"]}], "source": ["stock_dimension = len(trade.tic.unique())\n", "state_space = 1 + 2 * stock_dimension + len(INDICATORS) * stock_dimension\n", "print(f\"Stock Dimension: {stock_dimension}, State Space: {state_space}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "nKNmQMqGAknW"}, "outputs": [], "source": ["buy_cost_list = sell_cost_list = [0.001] * stock_dimension\n", "num_stock_shares = [0] * stock_dimension\n", "\n", "env_kwargs = {\n", "    \"hmax\": 100,\n", "    \"initial_amount\": 1000000,\n", "    \"num_stock_shares\": num_stock_shares,\n", "    \"buy_cost_pct\": buy_cost_list,\n", "    \"sell_cost_pct\": sell_cost_list,\n", "    \"state_space\": state_space,\n", "    \"stock_dim\": stock_dimension,\n", "    \"tech_indicator_list\": INDICATORS,\n", "    \"action_space\": stock_dimension,\n", "    \"reward_scaling\": 1e-4\n", "}"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "cIqoV0GSI52v"}, "outputs": [], "source": ["e_trade_gym = StockTradingEnv(df = trade, turbulence_threshold = 70,risk_indicator_col='vix', **env_kwargs)\n", "# env_trade, obs_trade = e_trade_gym.get_sb_env()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lbFchno5j3xs", "outputId": "44fffa47-3b47-4e7b-96c2-0a485e9efead"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hit end!\n"]}], "source": ["df_account_value_a2c, df_actions_a2c = DRLAgent.DRL_prediction(\n", "    model=trained_a2c, \n", "    environment = e_trade_gym) if if_using_a2c else (None, None)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "JbYljWGjj3pH"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hit end!\n"]}], "source": ["df_account_value_ddpg, df_actions_ddpg = DRLAgent.DRL_prediction(\n", "    model=trained_ddpg, \n", "    environment = e_trade_gym) if if_using_ddpg else (None, None)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "74jNP2DBj3hb"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hit end!\n"]}], "source": ["df_account_value_ppo, df_actions_ppo = DRLAgent.DRL_prediction(\n", "    model=trained_ppo, \n", "    environment = e_trade_gym) if if_using_ppo else (None, None)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "S7VyGGJPj3SH"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hit end!\n"]}], "source": ["df_account_value_td3, df_actions_td3 = DRLAgent.DRL_prediction(\n", "    model=trained_td3, \n", "    environment = e_trade_gym) if if_using_td3 else (None, None)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "eLOnL5eYh1jR", "outputId": "70e50e24-aed5-49f9-cdd7-de6b9689d9ce"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["hit end!\n"]}], "source": ["df_account_value_sac, df_actions_sac = DRLAgent.DRL_prediction(\n", "    model=trained_sac, \n", "    environment = e_trade_gym) if if_using_sac else (None, None)"]}, {"cell_type": "markdown", "metadata": {"id": "GcE-t08w6DaW"}, "source": ["# Part 3: Mean Variance Optimization"]}, {"cell_type": "markdown", "metadata": {"id": "17TUs71EWj09"}, "source": ["Mean Variance optimization is a very classic strategy in portfolio management. Here, we go through the whole process to do the mean variance optimization and add it as a baseline to compare.\n", "\n", "First, process dataframe to the form for MVO weight calculation."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "wungSNOwPwKR"}, "outputs": [], "source": ["def process_df_for_mvo(df):\n", "  return df.pivot(index=\"date\", columns=\"tic\", values=\"close\")"]}, {"cell_type": "markdown", "metadata": {"id": "SwEwkHJ1d_6u"}, "source": ["### Helper functions for mean returns and variance-covariance matrix"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "6KvXkpyE8MFq"}, "outputs": [], "source": ["# Codes in this section partially refer to Dr G <PERSON><PERSON>\n", "# https://www.kaggle.com/code/vijipai/lesson-5-mean-variance-optimization-of-portfolios/notebook\n", "\n", "def StockReturnsComputing(StockPrice, Rows, Columns): \n", "  import numpy as np \n", "  StockReturn = np.zeros([Rows-1, Columns]) \n", "  for j in range(Columns):        # j: Assets \n", "    for i in range(Rows-1):     # i: Daily Prices \n", "      StockReturn[i,j]=((StockPrice[i+1, j]-StockPrice[i,j])/StockPrice[i,j])* 100 \n", "      \n", "  return StockReturn"]}, {"cell_type": "markdown", "metadata": {"id": "IeVVbuwveJ_5"}, "source": ["### Calculate the weights for mean-variance"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kE8nruKLQYLO", "outputId": "42d07c80-f309-49f8-f2b4-36a51987086f"}, "outputs": [{"data": {"text/plain": ["array([[ 89.4945755 , 234.61436462,  90.74229431, ...,  46.98942566,\n", "         36.2900238 , 114.62765503],\n", "       [ 89.4945755 , 237.48353577,  91.0124588 , ...,  47.09257507,\n", "         37.26652145, 114.16794586],\n", "       [ 91.88856506, 235.65350342,  93.18331909, ...,  47.47934723,\n", "         38.31403732, 113.86148071],\n", "       ...,\n", "       [147.34196472, 197.85211182, 178.69203186, ...,  48.2805481 ,\n", "         45.98464584, 146.54455566],\n", "       [148.01603699, 198.85264587, 177.35902405, ...,  48.73966217,\n", "         45.13446426, 145.26525879],\n", "       [147.55014038, 196.8515625 , 174.49697876, ...,  48.32645416,\n", "         44.022686  , 144.07385254]])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["StockData = process_df_for_mvo(train)\n", "TradeData = process_df_for_mvo(trade)\n", "\n", "TradeData.to_numpy()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "u6_O6vrn_uD4", "outputId": "0c2f8bf7-07e7-4fe5-c409-93312b95a8dd"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean returns of assets in k-portfolio 1\n", " [0.136 0.068 0.086 0.083 0.066 0.134 0.06  0.035 0.072 0.056 0.103 0.073\n", " 0.033 0.076 0.047 0.073 0.042 0.056 0.054 0.056 0.103 0.089 0.041 0.053\n", " 0.104 0.11  0.044 0.042 0.042]\n", "Variance-Covariance matrix of returns\n", " [[3.156 1.066 1.768 1.669 1.722 1.814 1.569 1.302 1.302 1.811 1.303 1.432\n", "  1.218 1.674 0.74  1.839 0.719 0.884 1.241 0.823 1.561 1.324 0.752 1.027\n", "  1.298 1.466 0.657 1.078 0.631]\n", " [1.066 2.571 1.306 1.123 1.193 1.319 1.116 1.053 1.045 1.269 1.068 1.089\n", "  0.899 1.218 0.926 1.391 0.682 0.727 1.025 1.156 1.166 0.984 0.798 0.956\n", "  1.259 1.111 0.688 1.091 0.682]\n", " [1.768 1.306 4.847 2.73  2.6   2.128 1.944 2.141 2.17  3.142 1.932 2.283\n", "  1.56  2.012 0.993 3.707 1.094 1.319 1.845 1.236 1.899 1.894 1.041 1.921\n", "  1.823 2.314 0.986 1.421 0.707]\n", " [1.669 1.123 2.73  4.892 2.363 1.979 1.7   2.115 1.959 2.387 1.773 2.319\n", "  1.571 1.797 0.968 2.597 1.144 1.298 1.643 1.071 1.615 1.775 0.91  1.666\n", "  1.707 1.784 0.82  1.345 0.647]\n", " [1.722 1.193 2.6   2.363 4.019 2.127 1.917 2.059 1.817 2.46  1.577 2.238\n", "  1.513 1.929 0.925 2.64  0.947 0.971 1.894 1.089 1.711 1.642 0.865 1.456\n", "  1.478 1.687 0.92  1.326 0.697]\n", " [1.814 1.319 2.128 1.979 2.127 5.384 1.974 1.549 1.683 2.122 1.624 1.771\n", "  1.441 1.939 0.846 2.191 0.837 1.075 1.475 1.041 1.978 1.768 0.784 1.328\n", "  1.365 1.912 0.787 1.28  0.666]\n", " [1.569 1.116 1.944 1.7   1.917 1.974 3.081 1.483 1.534 1.937 1.367 1.62\n", "  1.399 1.843 0.894 2.057 0.794 0.905 1.438 1.014 1.72  1.382 0.865 1.206\n", "  1.273 1.488 0.811 1.173 0.753]\n", " [1.302 1.053 2.141 2.115 2.059 1.549 1.483 2.842 1.525 2.044 1.428 1.783\n", "  1.308 1.533 0.878 2.279 0.938 1.092 1.385 1.078 1.429 1.314 0.831 1.459\n", "  1.466 1.48  0.83  1.042 0.567]\n", " [1.302 1.045 2.17  1.959 1.817 1.683 1.534 1.525 2.661 1.987 1.454 1.748\n", "  1.217 1.475 0.791 2.216 0.896 0.973 1.396 0.949 1.379 1.407 0.859 1.268\n", "  1.281 1.454 0.81  1.143 0.667]\n", " [1.811 1.269 3.142 2.387 2.46  2.122 1.937 2.044 1.987 4.407 1.789 2.12\n", "  1.593 1.982 0.945 3.96  0.956 1.094 1.758 1.157 1.788 1.692 0.905 1.879\n", "  1.712 2.    0.945 1.421 0.713]\n", " [1.303 1.068 1.932 1.773 1.577 1.624 1.367 1.428 1.454 1.789 2.373 1.51\n", "  1.166 1.501 0.756 1.941 0.824 0.998 1.239 0.887 1.366 1.414 0.797 1.299\n", "  1.296 1.41  0.764 1.071 0.783]\n", " [1.432 1.089 2.283 2.319 2.238 1.771 1.62  1.783 1.748 2.12  1.51  2.516\n", "  1.326 1.575 0.889 2.345 0.958 1.022 1.623 1.02  1.489 1.532 0.848 1.377\n", "  1.444 1.547 0.81  1.211 0.63 ]\n", " [1.218 0.899 1.56  1.571 1.513 1.441 1.399 1.308 1.217 1.593 1.166 1.326\n", "  2.052 1.399 0.727 1.749 0.786 0.795 1.154 0.829 1.296 1.12  0.743 1.105\n", "  1.088 1.214 0.739 0.998 0.598]\n", " [1.674 1.218 2.012 1.797 1.929 1.939 1.843 1.533 1.475 1.982 1.501 1.575\n", "  1.399 3.289 0.853 2.112 0.85  0.89  1.412 1.002 1.9   1.352 0.842 1.317\n", "  1.334 1.487 0.847 1.165 0.766]\n", " [0.74  0.926 0.993 0.968 0.925 0.846 0.894 0.878 0.791 0.945 0.756 0.889\n", "  0.727 0.853 1.153 1.027 0.642 0.59  0.848 0.892 0.825 0.748 0.694 0.761\n", "  0.929 0.819 0.61  0.806 0.547]\n", " [1.839 1.391 3.707 2.597 2.64  2.191 2.057 2.279 2.216 3.96  1.941 2.345\n", "  1.749 2.112 1.027 5.271 1.08  1.235 1.892 1.297 1.91  1.85  1.068 2.164\n", "  1.85  2.169 1.112 1.555 0.779]\n", " [0.719 0.682 1.094 1.144 0.947 0.837 0.794 0.938 0.896 0.956 0.824 0.958\n", "  0.786 0.85  0.642 1.08  1.264 0.679 0.804 0.74  0.819 0.845 0.749 0.891\n", "  0.849 0.794 0.633 0.719 0.514]\n", " [0.884 0.727 1.319 1.298 0.971 1.075 0.905 1.092 0.973 1.094 0.998 1.022\n", "  0.795 0.89  0.59  1.235 0.679 1.518 0.816 0.719 0.943 1.027 0.615 1.\n", "  0.947 0.994 0.533 0.673 0.504]\n", " [1.241 1.025 1.845 1.643 1.894 1.475 1.438 1.385 1.396 1.758 1.239 1.623\n", "  1.154 1.412 0.848 1.892 0.804 0.816 2.028 0.9   1.265 1.243 0.787 1.194\n", "  1.193 1.282 0.752 1.099 0.622]\n", " [0.823 1.156 1.236 1.071 1.089 1.041 1.014 1.078 0.949 1.157 0.887 1.02\n", "  0.829 1.002 0.892 1.297 0.74  0.719 0.9   2.007 0.952 0.849 0.732 1.008\n", "  1.15  0.933 0.722 0.897 0.614]\n", " [1.561 1.166 1.899 1.615 1.711 1.978 1.72  1.429 1.379 1.788 1.366 1.489\n", "  1.296 1.9   0.825 1.91  0.819 0.943 1.265 0.952 2.759 1.308 0.832 1.214\n", "  1.285 1.493 0.793 1.113 0.705]\n", " [1.324 0.984 1.894 1.775 1.642 1.768 1.382 1.314 1.407 1.692 1.414 1.532\n", "  1.12  1.352 0.748 1.85  0.845 1.027 1.243 0.849 1.308 2.864 0.751 1.153\n", "  1.26  1.411 0.71  1.046 0.651]\n", " [0.752 0.798 1.041 0.91  0.865 0.784 0.865 0.831 0.859 0.905 0.797 0.848\n", "  0.743 0.842 0.694 1.068 0.749 0.615 0.787 0.732 0.832 0.751 1.289 0.806\n", "  0.766 0.763 0.663 0.797 0.645]\n", " [1.027 0.956 1.921 1.666 1.456 1.328 1.206 1.459 1.268 1.879 1.299 1.377\n", "  1.105 1.317 0.761 2.164 0.891 1.    1.194 1.008 1.214 1.153 0.806 2.27\n", "  1.259 1.294 0.812 0.986 0.676]\n", " [1.298 1.259 1.823 1.707 1.478 1.365 1.273 1.466 1.281 1.712 1.296 1.444\n", "  1.088 1.334 0.929 1.85  0.849 0.947 1.193 1.15  1.285 1.26  0.766 1.259\n", "  3.352 1.267 0.697 1.137 0.685]\n", " [1.466 1.111 2.314 1.784 1.687 1.912 1.488 1.48  1.454 2.    1.41  1.547\n", "  1.214 1.487 0.819 2.169 0.794 0.994 1.282 0.933 1.493 1.411 0.763 1.294\n", "  1.267 2.982 0.709 1.007 0.656]\n", " [0.657 0.688 0.986 0.82  0.92  0.787 0.811 0.83  0.81  0.945 0.764 0.81\n", "  0.739 0.847 0.61  1.112 0.633 0.533 0.752 0.722 0.793 0.71  0.663 0.812\n", "  0.697 0.709 1.371 0.697 0.561]\n", " [1.078 1.091 1.421 1.345 1.326 1.28  1.173 1.042 1.143 1.421 1.071 1.211\n", "  0.998 1.165 0.806 1.555 0.719 0.673 1.099 0.897 1.113 1.046 0.797 0.986\n", "  1.137 1.007 0.697 3.073 0.759]\n", " [0.631 0.682 0.707 0.647 0.697 0.666 0.753 0.567 0.667 0.713 0.783 0.63\n", "  0.598 0.766 0.547 0.779 0.514 0.504 0.622 0.614 0.705 0.651 0.645 0.676\n", "  0.685 0.656 0.561 0.759 1.452]]\n"]}], "source": ["#compute asset returns\n", "arStockPrices = np.asarray(StockData)\n", "[Rows, Cols]=arStockPrices.shape\n", "arReturns = StockReturnsComputing(arStockPrices, Rows, Cols)\n", "\n", "#compute mean returns and variance covariance matrix of returns\n", "meanReturns = np.mean(arReturns, axis = 0)\n", "covReturns = np.cov(arReturns, rowvar=False)\n", " \n", "#set precision for printing results\n", "np.set_printoptions(precision=3, suppress = True)\n", "\n", "#display mean returns and variance-covariance matrix of returns\n", "print('Mean returns of assets in k-portfolio 1\\n', meanReturns)\n", "print('Variance-Covariance matrix of returns\\n', covReturns)"]}, {"cell_type": "markdown", "metadata": {"id": "zC7r-cI8RR1X"}, "source": ["### Use PyPortfolioOpt"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "b1btTONEdCU4", "outputId": "75096462-7dfb-4ce6-c6f4-4671f11e79fc"}, "outputs": [{"data": {"text/plain": ["array([424250.,      0.,      0.,      0.,      0., 108650.,      0.,\n", "            0.,      0.,      0., 181450.,      0.,      0.,      0.,\n", "            0.,      0.,      0.,      0.,      0.,      0.,  16960.,\n", "            0.,      0.,      0., 133540., 135150.,      0.,      0.,\n", "            0.])"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["from pypfopt.efficient_frontier import EfficientFrontier\n", "\n", "ef_mean = EfficientFrontier(meanReturns, covReturns, weight_bounds=(0, 0.5))\n", "raw_weights_mean = ef_mean.max_sharpe()\n", "cleaned_weights_mean = ef_mean.clean_weights()\n", "mvo_weights = np.array([1000000 * cleaned_weights_mean[i] for i in range(len(cleaned_weights_mean))])\n", "mvo_weights"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "F38NJRJJgOmj", "outputId": "f575651b-1e9b-4015-ae71-c9fc2c3a3dae"}, "outputs": [{"data": {"text/plain": ["array([4731.544,    0.   ,    0.   ,    0.   ,    0.   ,  579.993,\n", "          0.   ,    0.   ,    0.   ,    0.   ,  771.759,    0.   ,\n", "          0.   ,    0.   ,    0.   ,    0.   ,    0.   ,    0.   ,\n", "          0.   ,    0.   ,   85.465,    0.   ,    0.   ,    0.   ,\n", "        470.265,  712.801,    0.   ,    0.   ,    0.   ])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["LastPrice = np.array([1/p for p in StockData.tail(1).to_numpy()[0]])\n", "Initial_Portfolio = np.multiply(mvo_weights, LastPrice)\n", "Initial_Portfolio"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"id": "ZAd1iXqZhQ6X"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Mean Var</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-07-01</th>\n", "      <td>1.001918e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-02</th>\n", "      <td>1.004235e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-06</th>\n", "      <td>1.023225e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-07</th>\n", "      <td>1.014021e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-08</th>\n", "      <td>1.029461e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-21</th>\n", "      <td>1.533022e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-22</th>\n", "      <td>1.535668e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-25</th>\n", "      <td>1.542078e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-26</th>\n", "      <td>1.545514e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-27</th>\n", "      <td>1.534916e+06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>335 rows × 1 columns</p>\n", "</div>"], "text/plain": ["                Mean Var\n", "date                    \n", "2020-07-01  1.001918e+06\n", "2020-07-02  1.004235e+06\n", "2020-07-06  1.023225e+06\n", "2020-07-07  1.014021e+06\n", "2020-07-08  1.029461e+06\n", "...                  ...\n", "2021-10-21  1.533022e+06\n", "2021-10-22  1.535668e+06\n", "2021-10-25  1.542078e+06\n", "2021-10-26  1.545514e+06\n", "2021-10-27  1.534916e+06\n", "\n", "[335 rows x 1 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["Portfolio_Assets = TradeData @ Initial_Portfolio\n", "MVO_result = pd.DataFrame(Portfolio_Assets, columns=[\"Mean Var\"])\n", "MVO_result"]}, {"cell_type": "markdown", "metadata": {"id": "I5sgGe7g1HsL"}, "source": ["# Part 4: DJIA index"]}, {"cell_type": "markdown", "metadata": {"id": "sVe_ufxTY2CW"}, "source": ["Add DJIA index as a baseline to compare with."]}, {"cell_type": "code", "execution_count": 21, "metadata": {"id": "sACPzsI-6k8q"}, "outputs": [], "source": ["TRAIN_START_DATE = '2009-01-01'\n", "TRAIN_END_DATE = '2020-07-01'\n", "TRADE_START_DATE = '2020-07-01'\n", "TRADE_END_DATE = '2021-10-29'"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "TuszW-OB1K0m", "outputId": "b89a8350-de58-4fea-8e4b-856efa872712"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[*********************100%***********************]  1 of 1 completed\n", "Shape of DataFrame:  (319, 8)\n"]}], "source": ["df_dji = YahooDownloader(\n", "    start_date=TRADE_START_DATE, end_date=TRADE_END_DATE, ticker_list=[\"dji\"]\n", ").fetch_data()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "Q3RXz72U1VbV"}, "outputs": [], "source": ["df_dji = df_dji[[\"date\", \"close\"]]\n", "fst_day = df_dji[\"close\"][0]\n", "dji = pd.merge(\n", "    df_dji[\"date\"],\n", "    df_dji[\"close\"].div(fst_day).mul(1000000),\n", "    how=\"outer\",\n", "    left_index=True,\n", "    right_index=True,\n", ").set_index(\"date\")"]}, {"cell_type": "markdown", "metadata": {"id": "W6vvNSC6h1jZ"}, "source": ["<a id='4'></a>\n", "# Part 5: Backtesting Results\n", "Backtesting plays a key role in evaluating the performance of a trading strategy. Automated backtesting tool is preferred because it reduces the human error. We usually use the Quantopian pyfolio package to backtest our trading strategies. It is easy to use and consists of various individual plots that provide a comprehensive image of the performance of a trading strategy."]}, {"cell_type": "code", "execution_count": 24, "metadata": {"id": "KeDeGAc9VrEg"}, "outputs": [], "source": ["df_result_a2c = (\n", "    df_account_value_a2c.set_index(df_account_value_a2c.columns[0])\n", "    if if_using_a2c\n", "    else None\n", ")\n", "df_result_ddpg = (\n", "    df_account_value_ddpg.set_index(df_account_value_ddpg.columns[0])\n", "    if if_using_ddpg\n", "    else None\n", ")\n", "df_result_ppo = (\n", "    df_account_value_ppo.set_index(df_account_value_ppo.columns[0])\n", "    if if_using_ppo\n", "    else None\n", ")\n", "df_result_td3 = (\n", "    df_account_value_td3.set_index(df_account_value_td3.columns[0])\n", "    if if_using_td3\n", "    else None\n", ")\n", "df_result_sac = (\n", "    df_account_value_sac.set_index(df_account_value_sac.columns[0])\n", "    if if_using_sac\n", "    else None\n", ")\n", "\n", "result = pd.DataFrame(\n", "    {\n", "        \"a2c\": df_result_a2c[\"account_value\"] if if_using_a2c else None,\n", "        \"ddpg\": df_result_ddpg[\"account_value\"] if if_using_ddpg else None,\n", "        \"ppo\": df_result_ppo[\"account_value\"] if if_using_ppo else None,\n", "        \"td3\": df_result_td3[\"account_value\"] if if_using_td3 else None,\n", "        \"sac\": df_result_sac[\"account_value\"] if if_using_sac else None,\n", "        \"mvo\": MVO_result[\"Mean Var\"],\n", "        \"dji\": dji[\"close\"],\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 455}, "id": "l4FZxyDt3XaE", "outputId": "2e739637-bf88-4698-9cf1-9a526452e465"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a2c</th>\n", "      <th>ddpg</th>\n", "      <th>ppo</th>\n", "      <th>td3</th>\n", "      <th>sac</th>\n", "      <th>mvo</th>\n", "      <th>dji</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-07-01</th>\n", "      <td>1.000000e+06</td>\n", "      <td>1.000000e+06</td>\n", "      <td>1.000000e+06</td>\n", "      <td>1.000000e+06</td>\n", "      <td>1.000000e+06</td>\n", "      <td>1.001918e+06</td>\n", "      <td>1.000000e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-02</th>\n", "      <td>1.000693e+06</td>\n", "      <td>1.000639e+06</td>\n", "      <td>1.000144e+06</td>\n", "      <td>1.000234e+06</td>\n", "      <td>1.000634e+06</td>\n", "      <td>1.004235e+06</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-06</th>\n", "      <td>1.006951e+06</td>\n", "      <td>1.006226e+06</td>\n", "      <td>1.001271e+06</td>\n", "      <td>1.006662e+06</td>\n", "      <td>1.005568e+06</td>\n", "      <td>1.023225e+06</td>\n", "      <td>1.021449e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-07</th>\n", "      <td>9.970032e+05</td>\n", "      <td>9.957683e+05</td>\n", "      <td>9.988985e+05</td>\n", "      <td>1.000661e+06</td>\n", "      <td>9.968734e+05</td>\n", "      <td>1.014021e+06</td>\n", "      <td>1.006031e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-08</th>\n", "      <td>1.001947e+06</td>\n", "      <td>1.001174e+06</td>\n", "      <td>9.989791e+05</td>\n", "      <td>1.005953e+06</td>\n", "      <td>1.000345e+06</td>\n", "      <td>1.029461e+06</td>\n", "      <td>1.012912e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-22</th>\n", "      <td>1.418043e+06</td>\n", "      <td>1.360597e+06</td>\n", "      <td>1.316574e+06</td>\n", "      <td>1.520761e+06</td>\n", "      <td>1.387252e+06</td>\n", "      <td>1.535668e+06</td>\n", "      <td>1.386322e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-25</th>\n", "      <td>1.419148e+06</td>\n", "      <td>1.362560e+06</td>\n", "      <td>1.323893e+06</td>\n", "      <td>1.525110e+06</td>\n", "      <td>1.385720e+06</td>\n", "      <td>1.542078e+06</td>\n", "      <td>1.388813e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-26</th>\n", "      <td>1.425101e+06</td>\n", "      <td>1.365712e+06</td>\n", "      <td>1.317819e+06</td>\n", "      <td>1.527354e+06</td>\n", "      <td>1.387495e+06</td>\n", "      <td>1.545514e+06</td>\n", "      <td>1.389427e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-27</th>\n", "      <td>1.420033e+06</td>\n", "      <td>1.355873e+06</td>\n", "      <td>1.327036e+06</td>\n", "      <td>1.532022e+06</td>\n", "      <td>1.383728e+06</td>\n", "      <td>1.534916e+06</td>\n", "      <td>1.379083e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-10-28</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.388401e+06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>336 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                     a2c          ddpg           ppo           td3   \n", "date                                                                 \n", "2020-07-01  1.000000e+06  1.000000e+06  1.000000e+06  1.000000e+06  \\\n", "2020-07-02  1.000693e+06  1.000639e+06  1.000144e+06  1.000234e+06   \n", "2020-07-06  1.006951e+06  1.006226e+06  1.001271e+06  1.006662e+06   \n", "2020-07-07  9.970032e+05  9.957683e+05  9.988985e+05  1.000661e+06   \n", "2020-07-08  1.001947e+06  1.001174e+06  9.989791e+05  1.005953e+06   \n", "...                  ...           ...           ...           ...   \n", "2021-10-22  1.418043e+06  1.360597e+06  1.316574e+06  1.520761e+06   \n", "2021-10-25  1.419148e+06  1.362560e+06  1.323893e+06  1.525110e+06   \n", "2021-10-26  1.425101e+06  1.365712e+06  1.317819e+06  1.527354e+06   \n", "2021-10-27  1.420033e+06  1.355873e+06  1.327036e+06  1.532022e+06   \n", "2021-10-28           NaN           NaN           NaN           NaN   \n", "\n", "                     sac           mvo           dji  \n", "date                                                  \n", "2020-07-01  1.000000e+06  1.001918e+06  1.000000e+06  \n", "2020-07-02  1.000634e+06  1.004235e+06           NaN  \n", "2020-07-06  1.005568e+06  1.023225e+06  1.021449e+06  \n", "2020-07-07  9.968734e+05  1.014021e+06  1.006031e+06  \n", "2020-07-08  1.000345e+06  1.029461e+06  1.012912e+06  \n", "...                  ...           ...           ...  \n", "2021-10-22  1.387252e+06  1.535668e+06  1.386322e+06  \n", "2021-10-25  1.385720e+06  1.542078e+06  1.388813e+06  \n", "2021-10-26  1.387495e+06  1.545514e+06  1.389427e+06  \n", "2021-10-27  1.383728e+06  1.534916e+06  1.379083e+06  \n", "2021-10-28           NaN           NaN  1.388401e+06  \n", "\n", "[336 rows x 7 columns]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "markdown", "metadata": {"id": "QQuc5hI9Yklt"}, "source": ["Now, everything is ready, we can plot the backtest result."]}, {"cell_type": "code", "execution_count": 26, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 381}, "id": "6xRfrqK4RVfq", "outputId": "469c9729-fd57-417c-9b13-2243426923e2"}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='date'>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}, {"data": {"text/plain": ["<Figure size 1500x500 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.rcParams[\"figure.figsize\"] = (15,5)\n", "plt.figure()\n", "result.plot()"]}], "metadata": {"colab": {"collapsed_sections": ["GfZ5vY5wRjkJ"], "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 0}