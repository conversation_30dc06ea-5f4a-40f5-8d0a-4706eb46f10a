{"cells": [{"cell_type": "markdown", "metadata": {"id": "3xt6fIDownZs"}, "source": ["# GPM: A graph convolutional network based reinforcement learning framework for portfolio management\n", "\n", "In this document, we will make use of a graph neural network architecture called GPM, introduced in the following paper:\n", "\n", "- <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> & <PERSON>. (2022). GPM: A graph convolutional network based reinforcement learning framework for portfolio management. https://doi.org/10.1016/j.neucom.2022.04.105.\n", "\n", "### Note\n", "If you're using the portfolio optimization environment, consider citing the following paper (in adittion to FinRL references):\n", "\n", "- <PERSON><PERSON><PERSON>, & <PERSON> (2023). POE: A General Portfolio Optimization Environment for FinRL. In *Anais do II Brazilian Workshop on Artificial Intelligence in Finance* (pp. 132–143). SBC. https://doi.org/10.5753/bwaif.2023.231144.\n", "\n", "```\n", "@inproceedings{bwaif,\n", " author = {<PERSON><PERSON><PERSON> and <PERSON>},\n", " title = {POE: A General Portfolio Optimization Environment for FinRL},\n", " booktitle = {Anais do <PERSON> Brazilian Workshop on Artificial Intelligence in Finance},\n", " location = {João <PERSON>/PB},\n", " year = {2023},\n", " keywords = {},\n", " issn = {0000-0000},\n", " pages = {132--143},\n", " publisher = {SBC},\n", " address = {Porto Alegre, RS, Brasil},\n", " doi = {10.5753/bwaif.2023.231144},\n", " url = {https://sol.sbc.org.br/index.php/bwaif/article/view/24959}\n", "}\n", "\n", "```"]}, {"cell_type": "markdown", "metadata": {"id": "Q0L7FZeWMUHp"}, "source": ["## Installation and imports\n", "\n", "To run this notebook in google colab, uncomment the cells below."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XGHfTt1HMVQw", "outputId": "e5226807-a740-4f22-a279-f466886518ba"}, "outputs": [], "source": ["## install finrl library\n", "# !sudo apt install swig\n", "# !pip install git+https://github.com/AI4Finance-Foundation/FinRL.git"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-GLganWiMYZ1", "outputId": "b3a7f99c-55dd-4274-c1ce-ab3a8111929a"}, "outputs": [], "source": ["## We also need to install quantstats, because the environment uses it to plot graphs\n", "# !pip install quantstats"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "6RqrzokqoanP"}, "outputs": [], "source": ["## Hide matplotlib warnings\n", "# import warnings\n", "# warnings.filterwarnings('ignore')\n", "\n", "import logging\n", "logging.getLogger('matplotlib.font_manager').disabled = True"]}, {"cell_type": "markdown", "metadata": {"id": "Cz8DLleGz_TF"}, "source": ["#### Import the necessary code libraries"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cP5t6U7-nYoc", "outputId": "fd138d3e-222a-4ec5-e008-03a28b89dae9"}, "outputs": [], "source": ["import torch\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from torch_geometric.utils import k_hop_subgraph\n", "\n", "from finrl.meta.preprocessor.yahoodownloader import YahooDownloader\n", "from finrl.meta.env_portfolio_optimization.env_portfolio_optimization import PortfolioOptimizationEnv\n", "from finrl.agents.portfolio_optimization.models import DRLAgent\n", "from finrl.agents.portfolio_optimization.architectures import GPM\n", "\n", "device = \"cuda:0\" if torch.cuda.is_available() else \"cpu\""]}, {"cell_type": "markdown", "metadata": {"id": "TY2yhvpASEyo"}, "source": ["## Fetch data\n", "\n", "We are going to use the same data used in the paper. The original data can be found in [Temporal_Relational_Stock_Ranking repository](https://github.com/fulifeng/Temporal_Relational_Stock_Ranking), but it's not in a FinRL friendly format. So, we're going to get the processed and FinRL-friendly data from [Temporal_Relational_Stock_Ranking_FinRL repository](https://github.com/C4i0kun/Temporal_Relational_Stock_Ranking_FinRL)."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NASDAQ_temporal_data.csv\n", "NYSE_temporal_data.csv\n"]}], "source": ["# download repository with data and extract tar.gz file with processed temporal data\n", "!curl -L -O https://github.com/C4i0kun/Temporal_Relational_Stock_Ranking_FinRL/archive/refs/heads/main.zip\n", "!unzip Temporal_Relational_Stock_Ranking_FinRL-main.zip\n", "!mv Temporal_Relational_Stock_Ranking_FinRL-main Temporal_Relational_Stock_Ranking_FinRL\n", "!tar -xzvf Temporal_Relational_Stock_Ranking_FinRL/temporal_data/temporal_data_processed.tar.gz -C Temporal_Relational_Stock_Ranking_FinRL/temporal_data"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "H11UjCstSFwm", "outputId": "3d27b983-d1e0-41af-b20a-421be40e469f"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>day</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>tic</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0.270533</td>\n", "      <td>0.269522</td>\n", "      <td>0.267237</td>\n", "      <td>0.263333</td>\n", "      <td>0.275333</td>\n", "      <td>AABA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0.238730</td>\n", "      <td>0.237522</td>\n", "      <td>0.239888</td>\n", "      <td>0.240502</td>\n", "      <td>0.248031</td>\n", "      <td>AAON</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>0.424556</td>\n", "      <td>0.424768</td>\n", "      <td>0.429769</td>\n", "      <td>0.442628</td>\n", "      <td>0.445018</td>\n", "      <td>AAPL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>0.650667</td>\n", "      <td>0.648412</td>\n", "      <td>0.634492</td>\n", "      <td>0.631473</td>\n", "      <td>0.667839</td>\n", "      <td>AAWW</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>0.766565</td>\n", "      <td>0.761693</td>\n", "      <td>0.759330</td>\n", "      <td>0.750061</td>\n", "      <td>0.788157</td>\n", "      <td>AAXJ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1277365</th>\n", "      <td>1244</td>\n", "      <td>0.909992</td>\n", "      <td>0.918809</td>\n", "      <td>0.912345</td>\n", "      <td>0.922698</td>\n", "      <td>0.913021</td>\n", "      <td>ZBRA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1277366</th>\n", "      <td>1244</td>\n", "      <td>0.660990</td>\n", "      <td>0.648907</td>\n", "      <td>0.636086</td>\n", "      <td>0.630816</td>\n", "      <td>0.665167</td>\n", "      <td>ZEUS</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1277367</th>\n", "      <td>1244</td>\n", "      <td>0.981600</td>\n", "      <td>0.961674</td>\n", "      <td>0.926768</td>\n", "      <td>0.914257</td>\n", "      <td>0.984345</td>\n", "      <td>ZION</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1277368</th>\n", "      <td>1244</td>\n", "      <td>0.980227</td>\n", "      <td>0.974903</td>\n", "      <td>0.960225</td>\n", "      <td>0.964956</td>\n", "      <td>0.997580</td>\n", "      <td>ZIV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1277369</th>\n", "      <td>1244</td>\n", "      <td>0.509179</td>\n", "      <td>0.500725</td>\n", "      <td>0.475543</td>\n", "      <td>0.459018</td>\n", "      <td>0.512077</td>\n", "      <td>ZUMZ</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1277370 rows × 7 columns</p>\n", "</div>"], "text/plain": ["          day      open      high       low     close    volume   tic\n", "0           0  0.270533  0.269522  0.267237  0.263333  0.275333  AABA\n", "1           0  0.238730  0.237522  0.239888  0.240502  0.248031  AAON\n", "2           0  0.424556  0.424768  0.429769  0.442628  0.445018  AAPL\n", "3           0  0.650667  0.648412  0.634492  0.631473  0.667839  AAWW\n", "4           0  0.766565  0.761693  0.759330  0.750061  0.788157  AAXJ\n", "...       ...       ...       ...       ...       ...       ...   ...\n", "1277365  1244  0.909992  0.918809  0.912345  0.922698  0.913021  ZBRA\n", "1277366  1244  0.660990  0.648907  0.636086  0.630816  0.665167  ZEUS\n", "1277367  1244  0.981600  0.961674  0.926768  0.914257  0.984345  ZION\n", "1277368  1244  0.980227  0.974903  0.960225  0.964956  0.997580   ZIV\n", "1277369  1244  0.509179  0.500725  0.475543  0.459018  0.512077  ZUMZ\n", "\n", "[1277370 rows x 7 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["nasdaq_temporal = pd.read_csv(\"Temporal_Relational_Stock_Ranking_FinRL/temporal_data/NASDAQ_temporal_data.csv\")\n", "nasdaq_temporal"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 623}, "id": "Bkm96aNsSIji", "outputId": "e3a20095-841e-4c89-c08e-24b9575cfb02"}, "outputs": [{"data": {"text/plain": ["array([[   0,   15,    0, ..., 1021, 1014, 1024],\n", "       [  15,    0,   18, ..., 1011, 1024, 1014]])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["nasdaq_edge_index = np.load(\"Temporal_Relational_Stock_Ranking_FinRL/relational_data/edge_indexes/NASDAQ_sector_industry_edge_index.npy\")\n", "nasdaq_edge_index"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0,  0,  0, ...,  1, 26, 26])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["nasdaq_edge_type = np.load(\"Temporal_Relational_Stock_Ranking_FinRL/relational_data/edge_types/NASDAQ_sector_industry_edge_type.npy\")\n", "nasdaq_edge_type"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Simplify Data\n", "\n", "The graph loaded is too big, causing the training process to be extremely slow. So we are going to remove some of the stocks in the graph structure so that only stocks from 2 hops of the ones in our portfolio are considered."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[2, 185, 215, 310, 395, 464, 596, 603, 637, 768, 1014]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["list_of_stocks = nasdaq_temporal[\"tic\"].unique().tolist()\n", "tics_in_portfolio = [\"AAPL\", \"CMCSA\", \"CSCO\", \"FB\", \"HBAN\", \"INTC\", \"MSFT\", \"MU\", \"NVDA\", \"QQQ\", \"XIV\"]\n", "\n", "portfolio_nodes = []\n", "for tic in tics_in_portfolio:\n", "    portfolio_nodes.append(list_of_stocks.index(tic))\n", "portfolio_nodes"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["nodes_kept, new_edge_index, nodes_to_select, edge_mask = k_hop_subgraph(\n", "    torch.LongTensor(portfolio_nodes),\n", "    2,\n", "    torch.from_numpy(nasdaq_edge_index),\n", "    relabel_nodes=True,\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>day</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>tic</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>0.424556</td>\n", "      <td>0.424768</td>\n", "      <td>0.429769</td>\n", "      <td>0.442628</td>\n", "      <td>0.445018</td>\n", "      <td>AAPL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>0.766565</td>\n", "      <td>0.761693</td>\n", "      <td>0.759330</td>\n", "      <td>0.750061</td>\n", "      <td>0.788157</td>\n", "      <td>AAXJ</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>0</td>\n", "      <td>0.244700</td>\n", "      <td>0.244212</td>\n", "      <td>0.238217</td>\n", "      <td>0.231041</td>\n", "      <td>0.251322</td>\n", "      <td>ABCB</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0</td>\n", "      <td>0.689403</td>\n", "      <td>0.698864</td>\n", "      <td>0.695715</td>\n", "      <td>0.675068</td>\n", "      <td>0.708124</td>\n", "      <td>ABCO</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>0</td>\n", "      <td>0.566419</td>\n", "      <td>0.564621</td>\n", "      <td>0.563147</td>\n", "      <td>0.560503</td>\n", "      <td>0.579132</td>\n", "      <td>ACIW</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1277358</th>\n", "      <td>1244</td>\n", "      <td>0.923544</td>\n", "      <td>0.922537</td>\n", "      <td>0.896003</td>\n", "      <td>0.888519</td>\n", "      <td>0.966042</td>\n", "      <td>XIV</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1277359</th>\n", "      <td>1244</td>\n", "      <td>0.919490</td>\n", "      <td>0.931946</td>\n", "      <td>0.950966</td>\n", "      <td>0.960872</td>\n", "      <td>0.920000</td>\n", "      <td>XLNX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1277361</th>\n", "      <td>1244</td>\n", "      <td>0.736906</td>\n", "      <td>0.747264</td>\n", "      <td>0.744030</td>\n", "      <td>0.745198</td>\n", "      <td>0.743781</td>\n", "      <td>YNDX</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1277367</th>\n", "      <td>1244</td>\n", "      <td>0.981600</td>\n", "      <td>0.961674</td>\n", "      <td>0.926768</td>\n", "      <td>0.914257</td>\n", "      <td>0.984345</td>\n", "      <td>ZION</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1277368</th>\n", "      <td>1244</td>\n", "      <td>0.980227</td>\n", "      <td>0.974903</td>\n", "      <td>0.960225</td>\n", "      <td>0.964956</td>\n", "      <td>0.997580</td>\n", "      <td>ZIV</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>517920 rows × 7 columns</p>\n", "</div>"], "text/plain": ["          day      open      high       low     close    volume   tic\n", "2           0  0.424556  0.424768  0.429769  0.442628  0.445018  AAPL\n", "4           0  0.766565  0.761693  0.759330  0.750061  0.788157  AAXJ\n", "7           0  0.244700  0.244212  0.238217  0.231041  0.251322  ABCB\n", "8           0  0.689403  0.698864  0.695715  0.675068  0.708124  ABCO\n", "13          0  0.566419  0.564621  0.563147  0.560503  0.579132  ACIW\n", "...       ...       ...       ...       ...       ...       ...   ...\n", "1277358  1244  0.923544  0.922537  0.896003  0.888519  0.966042   XIV\n", "1277359  1244  0.919490  0.931946  0.950966  0.960872  0.920000  XLNX\n", "1277361  1244  0.736906  0.747264  0.744030  0.745198  0.743781  YNDX\n", "1277367  1244  0.981600  0.961674  0.926768  0.914257  0.984345  ZION\n", "1277368  1244  0.980227  0.974903  0.960225  0.964956  0.997580   ZIV\n", "\n", "[517920 rows x 7 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# reduce temporal data\n", "nodes_kept = nodes_kept.tolist()\n", "nasdaq_temporal[\"tic_id\"], _ = pd.factorize(nasdaq_temporal[\"tic\"], sort=True)\n", "nasdaq_temporal = nasdaq_temporal[nasdaq_temporal[\"tic_id\"].isin(nodes_kept)]\n", "nasdaq_temporal = nasdaq_temporal.drop(columns=\"tic_id\")\n", "nasdaq_temporal"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0, 0, 0,  ..., 2, 6, 6])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# reduce edge type\n", "new_edge_type = torch.from_numpy(nasdaq_edge_type)[edge_mask]\n", "_, new_edge_type = torch.unique(new_edge_type, return_inverse=True)\n", "new_edge_type"]}, {"cell_type": "markdown", "metadata": {"id": "pM829994GWo3"}, "source": ["### Instantiate Environment\n", "\n", "Using the `PortfolioOptimizationEnv`, it's easy to instantiate a portfolio optimization environment for reinforcement learning agents. In the example below, we use the dataframe created before to start an environment."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["df_portfolio = nasdaq_temporal[[\"day\", \"tic\", \"close\", \"high\", \"low\"]]\n", "\n", "df_portfolio_train = df_portfolio[df_portfolio[\"day\"] < 979]\n", "df_portfolio_test = df_portfolio[df_portfolio[\"day\"] >= 979]\n", "\n", "environment_train = PortfolioOptimizationEnv(\n", "        df_portfolio_train,\n", "        initial_amount=100000,\n", "        comission_fee_pct=0.0025,\n", "        time_window=50,\n", "        features=[\"close\", \"high\", \"low\"],\n", "        time_column=\"day\",\n", "        normalize_df=None, # dataframe is already normalized\n", "        tics_in_portfolio=tics_in_portfolio\n", "    )\n", "\n", "environment_test = PortfolioOptimizationEnv(\n", "        df_portfolio_test,\n", "        initial_amount=100000,\n", "        comission_fee_pct=0.0025,\n", "        time_window=50,\n", "        features=[\"close\", \"high\", \"low\"],\n", "        time_column=\"day\",\n", "        normalize_df=None, # dataframe is already normalized\n", "        tics_in_portfolio=tics_in_portfolio\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Instantiate Model\n", "\n", "Now, we can instantiate the model using FinRL API. In this example, we are going to use the EI3 architecture introduced by <PERSON> et. al.\n", "\n", ":exclamation: **Note:** Remember to set the architecture's `time_window` parameter with the same value of the environment's `time_window`."]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["750b2ea28d2a439db3fc5034927dbce2", "c172e120fc5e4f9ab13bf8599d868b5f", "4b2aa7128c5d4d15bb794eb76faccd6a", "317393fb13c0449abfff29a4949553a0", "8cb75a82e5374c51b1f47a6e15783177", "9cb3d937be5d4f7cac192b392218ef37", "b27b9cc333ac44a5bb2cec60d02f16c0", "6a1187acb99d44c68e27cd5aad879ff1", "6a5c9dbaddc441d390d4827c170cbe9c", "1f84695a1caf4c80b29eb5eea90bb29a", "a7a6884bfdb642b9b342f7cda49d7d67"]}, "id": "wr82W3E0uQSo", "outputId": "61dcf1f5-7cf0-40b2-85bd-3f7dd943ddc6", "scrolled": true}, "outputs": [], "source": ["# set PolicyGradient parameters\n", "model_kwargs = {\n", "    \"lr\": 0.01,\n", "    \"policy\": GPM,\n", "}\n", "\n", "# here, we can set GPM's parameters\n", "policy_kwargs = {\n", "    \"edge_index\": new_edge_index,\n", "    \"edge_type\": new_edge_type,\n", "    \"nodes_to_select\": nodes_to_select\n", "}\n", "\n", "model = DRLAgent(environment_train).get_model(\"pg\", device, model_kwargs, policy_kwargs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Train Model\n", "\n", "We will train only a few episodes because training takes a considerable time."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|                                                     | 0/2 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 191986.328125\n", "Final accumulative portfolio value: 1.91986328125\n", "Maximum DrawDown: -0.20472381491579683\n", "Sharpe ratio: 5.4259683434925705\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 50%|██████████████████████                      | 1/2 [10:18<10:18, 618.80s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 292055.125\n", "Final accumulative portfolio value: 2.92055125\n", "Maximum DrawDown: -0.08291245970485872\n", "Sharpe ratio: 9.58044714250322\n", "=================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|████████████████████████████████████████████| 2/2 [20:37<00:00, 618.95s/it]\n"]}, {"data": {"text/plain": ["<finrl.agents.portfolio_optimization.algorithms.PolicyGradient at 0x7f43395e0310>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["DRLAgent.train_model(model, episodes=2)"]}, {"cell_type": "markdown", "metadata": {"id": "JE7X3qEeXOr4"}, "source": ["### Save Model"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "YcWuPgPvXNpP"}, "outputs": [], "source": ["torch.save(model.train_policy.state_dict(), \"policy_GPM.pt\")"]}, {"cell_type": "markdown", "metadata": {"id": "7FRK9A98XVck"}, "source": ["## Test Model"]}, {"cell_type": "markdown", "metadata": {"id": "pstJ-uY1_7VY"}, "source": ["Following the idea from the original article, we will evaluate the performance of the trained model in the test period. We will also compare with the Uniform buy and hold strategy."]}, {"cell_type": "markdown", "metadata": {"id": "Y4RuS2pRAa4H"}, "source": ["### Test GPM architecture\n", "\n", "It's important no note that, in this code, we load the saved policy even though it's not necessary just to show how to save and load your model."]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "JeRy__TI9CAs"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 143342.140625\n", "Final accumulative portfolio value: 1.43342140625\n", "Maximum DrawDown: -0.0031984947828825883\n", "Sharpe ratio: 21.581835787662943\n", "=================================\n"]}], "source": ["GPM_results = {\n", "    \"train\": environment_train._asset_memory[\"final\"],\n", "    \"test\": {},\n", "}\n", "\n", "# instantiate an architecture with the same arguments used in training\n", "# and load with load_state_dict.\n", "policy = GPM(new_edge_index, new_edge_type, nodes_to_select, device=device)\n", "policy.load_state_dict(torch.load(\"policy_GPM.pt\"))\n", "\n", "# testing\n", "DRLAgent.DRL_validation(model, environment_test, policy=policy)\n", "GPM_results[\"test\"] = environment_test._asset_memory[\"final\"]"]}, {"cell_type": "markdown", "metadata": {"id": "LZc5PpbaBU-J"}, "source": ["### Test Uniform Buy and Hold\n", "For comparison, we will also test the performance of a uniform buy and hold strategy. In this strategy, the portfolio has no remaining cash and the same percentage of money is allocated in each asset."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "ntHO_UIs-83T"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 210066.515625\n", "Final accumulative portfolio value: 2.10066515625\n", "Maximum DrawDown: -0.1770357694310173\n", "Sharpe ratio: 6.126976338415281\n", "=================================\n", "=================================\n", "Initial portfolio value:100000\n", "Final portfolio value: 140385.78125\n", "Final accumulative portfolio value: 1.4038578125\n", "Maximum DrawDown: -0.001439125798492591\n", "Sharpe ratio: 23.930156872458472\n", "=================================\n"]}], "source": ["UBAH_results = {\n", "    \"train\": {},\n", "    \"test\": {},\n", "}\n", "\n", "PORTFOLIO_SIZE = len(tics_in_portfolio)\n", "\n", "# train period\n", "terminated = False\n", "environment_train.reset()\n", "while not terminated:\n", "    action = [0] + [1/PORTFOLIO_SIZE] * PORTFOLIO_SIZE\n", "    _, _, terminated, _ = environment_train.step(action)\n", "UBAH_results[\"train\"] = environment_train._asset_memory[\"final\"]\n", "\n", "# test period\n", "terminated = False\n", "environment_test.reset()\n", "while not terminated:\n", "    action = [0] + [1/PORTFOLIO_SIZE] * PORTFOLIO_SIZE\n", "    _, _, terminated, _ = environment_test.step(action)\n", "UBAH_results[\"test\"] = environment_test._asset_memory[\"final\"]"]}, {"cell_type": "markdown", "metadata": {"id": "kBMM7hAHC6rq"}, "source": ["### Plot graphics"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"id": "n8YrDNpeC71w"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "%matplotlib inline \n", "\n", "plt.plot(UBAH_results[\"train\"], label=\"Buy and Hold\")\n", "plt.plot(GPM_results[\"train\"], label=\"GPM\")\n", "\n", "plt.xlabel(\"Days\")\n", "plt.ylabel(\"Portfolio Value\")\n", "plt.title(\"Performance in training period\")\n", "plt.legend()\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"id": "dQniascoDIH2"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(UBAH_results[\"test\"], label=\"Buy and Hold\")\n", "plt.plot(GPM_results[\"test\"], label=\"GPM\")\n", "\n", "plt.xlabel(\"Days\")\n", "plt.ylabel(\"Portfolio Value\")\n", "plt.title(\"Performance in testing period\")\n", "plt.legend()\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With only two training episodes, we can see that GPM achieves better performance than buy and hold strategy, but according to the original article, that performance could be better. Hyperparameter tuning must be performed. Additionaly, we used softmax temperature equal to one, something that can be changed to achieve better performance as stated in the original article."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"1f84695a1caf4c80b29eb5eea90bb29a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "317393fb13c0449abfff29a4949553a0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1f84695a1caf4c80b29eb5eea90bb29a", "placeholder": "​", "style": "IPY_MODEL_a7a6884bfdb642b9b342f7cda49d7d67", "value": " 10/250 [05:53&lt;2:10:07, 32.53s/it]"}}, "4b2aa7128c5d4d15bb794eb76faccd6a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6a1187acb99d44c68e27cd5aad879ff1", "max": 250, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6a5c9dbaddc441d390d4827c170cbe9c", "value": 10}}, "6a1187acb99d44c68e27cd5aad879ff1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6a5c9dbaddc441d390d4827c170cbe9c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "750b2ea28d2a439db3fc5034927dbce2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c172e120fc5e4f9ab13bf8599d868b5f", "IPY_MODEL_4b2aa7128c5d4d15bb794eb76faccd6a", "IPY_MODEL_317393fb13c0449abfff29a4949553a0"], "layout": "IPY_MODEL_8cb75a82e5374c51b1f47a6e15783177"}}, "8cb75a82e5374c51b1f47a6e15783177": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9cb3d937be5d4f7cac192b392218ef37": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a7a6884bfdb642b9b342f7cda49d7d67": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b27b9cc333ac44a5bb2cec60d02f16c0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c172e120fc5e4f9ab13bf8599d868b5f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9cb3d937be5d4f7cac192b392218ef37", "placeholder": "​", "style": "IPY_MODEL_b27b9cc333ac44a5bb2cec60d02f16c0", "value": "  4%"}}}}}, "nbformat": 4, "nbformat_minor": 4}