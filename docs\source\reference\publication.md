# Publications

Papers by the Columbia research team can be found at [Google Scholar](https://scholar.google.com/citations?view_op=list_works&hl=en&hl=en&user=XsdPXocAAAAJ).

|Title |Conference |Link|Citations|Year|
|  ----  |  ----  |  ----  |  ----  |  ----  |
|**FinRL-Meta**: A Universe of Near-Real Market Environments for Data-Driven Deep Reinforcement Learning in Quantitative Finance| NeurIPS 2021 Data-Centric AI Workshop| [paper](https://arxiv.org/abs/2112.06753) ;<br />[code](https://github.com/AI4Finance-Foundation/FinRL-Meta)| 2| 2021|
|Explainable deep reinforcement learning for portfolio management: An empirical approach| ICAIF 2021 : ACM International Conference on AI in Finance | [paper](https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3958005);<br />[code](https://github.com/AI4Finance-Foundation/FinRL)| 1| 2021|
|**FinRL-Podracer**: High performance and scalable deep reinforcement learning for quantitative finance| ICAIF 2021 : ACM International Conference on AI in Finance | [paper](https://arxiv.org/abs/2111.05188);<br />[code](https://github.com/AI4Finance-Foundation/FinRL_Podracer)| 2 | 2021|
|**FinRL**: Deep reinforcement learning framework to automate trading in quantitative finance| ICAIF 2021 : ACM International Conference on AI in Finance | [paper](https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3955949);<br />[code](https://github.com/AI4Finance-Foundation/FinRL)| 4| 2021|
|**FinRL**: A deep reinforcement learning library for automated stock trading in quantitative finance| NeurIPS 2020 Deep RL Workshop  | [paper](https://arxiv.org/abs/2011.09607);<br />[code](https://github.com/AI4Finance-Foundation/FinRL)| 20| 2020|
|Deep reinforcement learning for automated stock trading: An ensemble strategy| ICAIF 2020 : ACM International Conference on AI in Finance | [paper](https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3690996);<br />[code](https://github.com/AI4Finance-Foundation/Deep-Reinforcement-Learning-for-Automated-Stock-Trading-Ensemble-Strategy-ICAIF-2020)| 46 | 2020|
|Multi-agent reinforcement learning for liquidation strategy analysis| ICML 2019 Workshop on AI in Finance: Applications and Infrastructure for Multi-Agent Learning| [paper](https://arxiv.org/abs/1906.11046); <br />[code](https://github.com/AI4Finance-Foundation/Liquidation-Analysis-using-Multi-Agent-Reinforcement-Learning-ICML-2019)| 19 | 2019|
|Practical deep reinforcement learning approach for stock trading| NeurIPS 2018 Workshop on Challenges and Opportunities for AI in Financial Services| [paper](https://arxiv.org/abs/1811.07522); <br />[code](https://github.com/AI4Finance-Foundation/DQN-DDPG_Stock_Trading)| 87| 2018 |
