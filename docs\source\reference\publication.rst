Publications
=============

Papers by the Columbia research team can be found at `Google Scholar <https://scholar.google.com/citations?view_op=list_works&hl=en&hl=en&user=XsdPXocAAAAJ>`_.

.. list-table:: Publications
   :widths: 20 20 40 10 10
   :header-rows: 1

   * - Title
     - Conference
     - Link
     - Citations
     - Year
   * - **FinRL-Meta**: A Universe of Near-Real Market Environments for Data-Driven Deep Reinforcement Learning in Quantitative Finance
     - NeurIPS 2021 Data-Centric AI Workshop
     - `paper <https://arxiv.org/abs/2112.06753>`_, `code <https://github.com/AI4Finance-Foundation/FinRL-Meta>`_
     - 2
     - 2021
   * - Explainable deep reinforcement learning for portfolio management: An empirical approach
     - ICAIF 2021: ACM International Conference on AI in Finance
     - `paper <https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3958005>`_, `code <https://github.com/AI4Finance-Foundation/FinRL>`_
     - 1
     - 2021
   * - **FinRL-Podracer**: High performance and scalable deep reinforcement learning for quantitative finance
     - ICAIF 2021: ACM International Conference on AI in Finance
     - `paper <https://arxiv.org/abs/2111.05188>`_, `code <https://github.com/AI4Finance-Foundation/FinRL_Podracer>`_
     - 2
     - 2021
   * - **FinRL**: Deep reinforcement learning framework to automate trading in quantitative finance
     - ICAIF 2021: ACM International Conference on AI in Finance
     - `paper <https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3955949>`_, `code <https://github.com/AI4Finance-Foundation/FinRL>`_
     - 7
     - 2021
   * - **FinRL**: A deep reinforcement learning library for automated stock trading in quantitative finance
     - NeurIPS 2020 Deep RL Workshop
     - `paper <https://arxiv.org/abs/2011.09607>`_, `code <https://github.com/AI4Finance-Foundation/FinRL>`_
     - 25
     - 2020
   * - Deep reinforcement learning for automated stock trading: An ensemble strategy
     - ICAIF 2020: ACM International Conference on AI in Finance
     - `paper <https://papers.ssrn.com/sol3/papers.cfm?abstract_id=3690996>`_, `code <https://github.com/AI4Finance-Foundation/Deep-Reinforcement-Learning-for-Automated-Stock-Trading-Ensemble-Strategy-ICAIF-2020>`_
     - 44
     - 2020
   * - Multi-agent reinforcement learning for liquidation strategy analysis
     - ICML 2019 Workshop on AI in Finance: Applications and Infrastructure for Multi-Agent Learning
     - `paper <https://arxiv.org/abs/1906.11046>`_, `code <https://github.com/AI4Finance-Foundation/Liquidation-Analysis-using-Multi-Agent-Reinforcement-Learning-ICML-2019>`_
     - 19
     - 2019
   * - Practical deep reinforcement learning approach for stock trading
     - NeurIPS 2018 Workshop on Challenges and Opportunities for AI in Financial Services
     - `paper <https://arxiv.org/abs/1811.07522>`_, `code <https://github.com/AI4Finance-Foundation/DQN-DDPG_Stock_Trading>`_
     - 86
     - 2018
